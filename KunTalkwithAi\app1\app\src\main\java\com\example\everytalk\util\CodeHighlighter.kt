package com.example.everytalk.util

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern

/**
 * Defines the color scheme for syntax highlighting.
 * This allows for easy theme switching.
 */
data class SyntaxTheme(
    val comment: Color,
    val punctuation: Color,
    val keyword: Color,
    val operator: Color,
    val type: Color,
    val function: Color,
    val string: Color,
    val number: Color,
    val variable: Color,
    val annotation: Color,
    val attribute: Color,
    val tag: Color,
    val value: Color,
    val cssSelector: Color,
    val cssProperty: Color,
    val module: Color
)

/**
 * A beautiful, light theme inspired by Catppuccin Latte.
 */
val CatppuccinLatteTheme = SyntaxTheme(
    comment = Color(0xFF9CA0B0),
    punctuation = Color(0xFF888B9D),
    keyword = Color(0xFF8839EF),
    operator = Color(0xFF179299),
    type = Color(0xFFD20F39), // Changed for better visibility
    function = Color(0xFF1E66F5),
    string = Color(0xFF40A02B),
    number = Color(0xFFFE640B),
    variable = Color(0xFF4C4F69),
    annotation = Color(0xFFD20F39),
    attribute = Color(0xFFDD7878),
    tag = Color(0xFF1E66F5),
    value = Color(0xFF179299),
    cssSelector = Color(0xFF7287FD),
    cssProperty = Color(0xFF1E66F5),
    module = Color(0xFF7287FD)
)

object CodeHighlighter {

    private data class Rule(val pattern: Pattern, val color: (SyntaxTheme) -> Color, val groupIndex: Int = 1)

    // Cache for compiled language rules to avoid re-compilation on every call.
    private val languageRuleCache = ConcurrentHashMap<String, List<Rule>>()

    private fun getRules(language: String?, theme: SyntaxTheme): List<Rule> {
        val lang = language?.lowercase()?.trim() ?: "text"
        return languageRuleCache.getOrPut(lang) {
            // The order of rules is critical. More specific rules must come first.
            when (lang) {
                "html" -> listOf(
                    Rule(Pattern.compile("<!--[\\s\\S]*?-->"), { it.comment }, 0),
                    Rule(Pattern.compile("(<\\/?)([a-zA-Z0-9\\-]+)"), { it.tag }, 2),
                    Rule(Pattern.compile("\\s([a-zA-Z\\-]+)(?=\\s*=)"), { it.attribute }, 1),
                    Rule(Pattern.compile("(\"[^\"]*\"|'[^']*')"), { it.value }, 1),
                    Rule(Pattern.compile("(&[a-zA-Z0-9#]+;)"), { it.punctuation }, 1),
                    Rule(Pattern.compile("([<>/=])"), { it.punctuation }, 1)
                )
                "css" -> listOf(
                    Rule(Pattern.compile("\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"[^\"]*\"|'[^']*')"), { it.value }, 1),
                    Rule(Pattern.compile("(#[a-fA-F0-9]{3,8})\\b"), { it.value }, 1),
                    Rule(Pattern.compile("\\b(body|html|div|span|a|p|h[1-6])\\b(?=[\\s,{])"), { it.cssSelector }, 1),
                    Rule(Pattern.compile("([#.]-?[_a-zA-Z]+[_a-zA-Z0-9-]*)(?=[\\s,{.:])"), { it.cssSelector }, 1),
                    Rule(Pattern.compile("(:[:]?[-a-zA-Z_0-9]+)"), { it.cssSelector }, 1),
                    Rule(Pattern.compile("\\b([a-zA-Z\\-]+)(?=\\s*:)"), { it.cssProperty }, 1),
                    Rule(Pattern.compile("\\b(-?(?:[0-9]+(?:\\.[0-9]*)?|\\.[0-9]+)(?:px|em|rem|%|vh|vw|s|ms|deg|turn|fr)?)\\b"), { it.value }, 1),
                    Rule(Pattern.compile("([+\\-*/%])"), { it.operator }, 1),
                    Rule(Pattern.compile("([:;{}()\\[\\]])"), { it.punctuation }, 1)
                )
                "javascript", "js", "typescript", "ts" -> {
                    val keywords = "const|let|var|function|return|if|else|for|while|switch|case|break|continue|new|this|import|export|from|default|async|await|try|catch|finally|class|extends|super|delete|in|instanceof|typeof|void|get|set|public|private|protected|readonly|enum|type|interface|implements|declare|namespace"
                    listOf(
                        Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                        Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|`(?:\\\\.|[^`\\\\])*`)"), { it.string }),
                        Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                        Rule(Pattern.compile("\\b($keywords)\\b"), { it.keyword }),
                        Rule(Pattern.compile("\\b(document|window|console|Math|JSON|Promise|Array|Object|String|Number|Boolean|Date|RegExp|any|string|number|boolean|void|null|undefined|never)\\b"), { it.type }),
                        Rule(Pattern.compile("\\b(true|false|null|undefined)\\b"), { it.keyword }),
                        Rule(Pattern.compile("(@[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                        Rule(Pattern.compile("(?<=[\\s.(,])(?!($keywords)\\b)([a-zA-Z_][a-zA-Z0-9_]*)(?=\\s*\\()"), { it.function }),
                        Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                        Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                    )
                }
                "python", "py" -> listOf(
                    Rule(Pattern.compile("#.*"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"\"\"[\\s\\S]*?\"\"\"|'''[\\s\\S]*?'''|\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(def|class|if|else|elif|for|while|return|import|from|as|try|except|finally|with|lambda|pass|break|continue|in|is|not|and|or|True|False|None|self|async|await|yield|global|nonlocal|assert|del|raise)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(int|str|float|list|dict|tuple|set|bool|object|bytes|bytearray|complex|frozenset|range|type|Exception|ValueError|TypeError|KeyError|IndexError|AttributeError)\\b"), { it.type }),
                    Rule(Pattern.compile("(@[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                    Rule(Pattern.compile("(?<=\\bdef\\s)([a-zA-Z0-9_]+)"), { it.function }),
                    Rule(Pattern.compile("(?<=\\bclass\\s)([a-zA-Z0-9_]+)"), { it.type }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([:,.()\\[\\]{}])"), { it.punctuation })
                )
                "java" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?[fFdDlL]?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(public|private|protected|static|final|abstract|synchronized|volatile|transient|native|strictfp|class|interface|enum|extends|implements|import|package|if|else|for|while|do|switch|case|default|break|continue|return|try|catch|finally|throw|throws|new|this|super|instanceof|null|true|false|void)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(int|long|short|byte|char|float|double|boolean|String|Object|Integer|Long|Short|Byte|Character|Float|Double|Boolean|List|Map|Set|ArrayList|HashMap|HashSet)\\b"), { it.type }),
                    Rule(Pattern.compile("(@[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                    Rule(Pattern.compile("(?<=\\b(?:public|private|protected|static|final|abstract)\\s+)*(?:[a-zA-Z_][a-zA-Z0-9_]*\\s+)*([a-zA-Z_][a-zA-Z0-9_]*)(?=\\s*\\()"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "kotlin", "kt" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?[fFdDlL]?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(fun|val|var|class|interface|object|enum|data|sealed|abstract|open|final|override|private|public|protected|internal|if|else|when|for|while|do|break|continue|return|try|catch|finally|throw|import|package|as|is|in|out|by|where|init|constructor|companion|lateinit|lazy|suspend|inline|noinline|crossinline|reified|vararg|tailrec|operator|infix|external|annotation|expect|actual|null|true|false|this|super)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(Int|Long|Short|Byte|Char|Float|Double|Boolean|String|Any|Unit|Nothing|List|MutableList|Map|MutableMap|Set|MutableSet|Array|IntArray|LongArray|FloatArray|DoubleArray|BooleanArray|CharArray|ByteArray|ShortArray)\\b"), { it.type }),
                    Rule(Pattern.compile("(@[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                    Rule(Pattern.compile("(?<=\\bfun\\s+)([a-zA-Z_][a-zA-Z0-9_]*)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "c", "cpp", "c++" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?[fFdDlLuU]*)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|restrict|return|short|signed|sizeof|static|struct|switch|typedef|union|unsigned|void|volatile|while|bool|true|false|nullptr|class|private|public|protected|virtual|override|final|namespace|using|template|typename|try|catch|throw|new|delete|this|operator)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(int|char|float|double|void|bool|long|short|unsigned|signed|const|volatile|auto|string|vector|map|set|list|queue|stack|pair|tuple)\\b"), { it.type }),
                    Rule(Pattern.compile("(#[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                    Rule(Pattern.compile("\\b([a-zA-Z_][a-zA-Z0-9_]*)(?=\\s*\\()"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^~])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "rust", "rs" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?[fFdDlLuU]*)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(fn|let|mut|const|static|if|else|match|loop|while|for|in|break|continue|return|impl|trait|struct|enum|mod|pub|use|crate|super|self|Self|where|async|await|move|ref|dyn|unsafe|extern|type|as|true|false)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(i8|i16|i32|i64|i128|isize|u8|u16|u32|u64|u128|usize|f32|f64|bool|char|str|String|Vec|HashMap|HashSet|Option|Result|Box|Rc|Arc|RefCell|Mutex|RwLock)\\b"), { it.type }),
                    Rule(Pattern.compile("(#\\[[^\\]]*\\])"), { it.annotation }),
                    Rule(Pattern.compile("(?<=\\bfn\\s+)([a-zA-Z_][a-zA-Z0-9_]*)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "go" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*'|`[^`]*`)"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go|goto|if|import|interface|map|package|range|return|select|struct|switch|type|var|true|false|nil|iota)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(bool|byte|complex64|complex128|error|float32|float64|int|int8|int16|int32|int64|rune|string|uint|uint8|uint16|uint32|uint64|uintptr)\\b"), { it.type }),
                    Rule(Pattern.compile("(?<=\\bfunc\\s+)([a-zA-Z_][a-zA-Z0-9_]*)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^:])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "swift" -> listOf(
                    Rule(Pattern.compile("//.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(class|struct|enum|protocol|extension|func|var|let|if|else|switch|case|default|for|while|repeat|break|continue|return|import|public|private|internal|fileprivate|open|final|static|override|required|convenience|lazy|weak|unowned|mutating|nonmutating|dynamic|inout|associatedtype|typealias|true|false|nil|self|Self|super|throws|rethrows|try|catch|defer|guard|where|is|as|in)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(Int|Double|Float|String|Bool|Character|Array|Dictionary|Set|Optional|Any|AnyObject|Void|Never)\\b"), { it.type }),
                    Rule(Pattern.compile("(@[a-zA-Z_][a-zA-Z0-9_]*)"), { it.annotation }),
                    Rule(Pattern.compile("(?<=\\bfunc\\s+)([a-zA-Z_][a-zA-Z0-9_]*)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "php" -> listOf(
                    Rule(Pattern.compile("//.*|#.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(abstract|and|array|as|break|callable|case|catch|class|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|eval|exit|extends|final|finally|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|namespace|new|or|print|private|protected|public|require|require_once|return|static|switch|throw|trait|try|unset|use|var|while|xor|true|false|null)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(string|int|float|bool|array|object|resource|null|mixed|callable|iterable|void)\\b"), { it.type }),
                    Rule(Pattern.compile("(\\$[a-zA-Z_][a-zA-Z0-9_]*)"), { it.variable }),
                    Rule(Pattern.compile("(?<=\\bfunction\\s+)([a-zA-Z_][a-zA-Z0-9_]*)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^.])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "ruby", "rb" -> listOf(
                    Rule(Pattern.compile("#.*"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(alias|and|begin|break|case|class|def|defined|do|else|elsif|end|ensure|false|for|if|in|module|next|nil|not|or|redo|rescue|retry|return|self|super|then|true|undef|unless|until|when|while|yield|require|include|extend|attr_reader|attr_writer|attr_accessor|private|protected|public)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(String|Integer|Float|Array|Hash|Symbol|Regexp|Class|Module|Object|Numeric|TrueClass|FalseClass|NilClass)\\b"), { it.type }),
                    Rule(Pattern.compile("(:[a-zA-Z_][a-zA-Z0-9_]*[?!]?)"), { it.value }),
                    Rule(Pattern.compile("(?<=\\bdef\\s+)([a-zA-Z_][a-zA-Z0-9_]*[?!]?)"), { it.function }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}])"), { it.punctuation })
                )
                "shell", "bash", "sh" -> listOf(
                    Rule(Pattern.compile("#.*"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(if|then|else|elif|fi|case|esac|for|while|until|do|done|function|return|break|continue|exit|export|local|readonly|declare|unset|shift|eval|exec|source|alias|unalias|type|which|command|builtin|enable|help|let|mapfile|printf|read|readarray|test|time|trap|ulimit|umask|wait|jobs|bg|fg|disown|kill|nohup|true|false)\\b"), { it.keyword }),
                    Rule(Pattern.compile("(\\$[a-zA-Z_][a-zA-Z0-9_]*|\\$\\{[^}]*\\}|\\$[0-9@#?*!$-])"), { it.variable }),
                    Rule(Pattern.compile("([=+\\-*/%<>!&|?^])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]{}|&])"), { it.punctuation })
                )
                "sql" -> listOf(
                    Rule(Pattern.compile("--.*|\\/\\*[\\s\\S]*?\\*\\/"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(?i)(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|VIEW|DATABASE|SCHEMA|GRANT|REVOKE|COMMIT|ROLLBACK|TRANSACTION|BEGIN|END|IF|ELSE|CASE|WHEN|THEN|AS|AND|OR|NOT|IN|EXISTS|BETWEEN|LIKE|IS|NULL|TRUE|FALSE|DISTINCT|GROUP|ORDER|BY|HAVING|LIMIT|OFFSET|JOIN|INNER|LEFT|RIGHT|FULL|OUTER|ON|UNION|ALL|ANY|SOME|PRIMARY|KEY|FOREIGN|REFERENCES|UNIQUE|CHECK|DEFAULT|AUTO_INCREMENT|IDENTITY)\\b"), { it.keyword }),
                    Rule(Pattern.compile("\\b(?i)(INT|INTEGER|BIGINT|SMALLINT|TINYINT|DECIMAL|NUMERIC|FLOAT|REAL|DOUBLE|CHAR|VARCHAR|TEXT|DATE|TIME|DATETIME|TIMESTAMP|BOOLEAN|BOOL|BINARY|VARBINARY|BLOB|CLOB)\\b"), { it.type }),
                    Rule(Pattern.compile("([=+\\-*/%<>!])"), { it.operator }),
                    Rule(Pattern.compile("([;,.()\\[\\]])"), { it.punctuation })
                )
                "json" -> listOf(
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\")(?=\\s*:)"), { it.attribute }),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\")(?!\\s*:)"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(true|false|null)\\b"), { it.keyword }),
                    Rule(Pattern.compile("([{}\\[\\],:])"), { it.punctuation })
                )
                "xml" -> listOf(
                    Rule(Pattern.compile("<!--[\\s\\S]*?-->"), { it.comment }, 0),
                    Rule(Pattern.compile("(<\\?[\\s\\S]*?\\?>)"), { it.annotation }, 0),
                    Rule(Pattern.compile("(<!DOCTYPE[\\s\\S]*?>)"), { it.annotation }, 0),
                    Rule(Pattern.compile("(<\\/?)([a-zA-Z0-9\\-:]+)"), { it.tag }, 2),
                    Rule(Pattern.compile("\\s([a-zA-Z\\-:]+)(?=\\s*=)"), { it.attribute }, 1),
                    Rule(Pattern.compile("(\"[^\"]*\"|'[^']*')"), { it.value }, 1),
                    Rule(Pattern.compile("([<>/=])"), { it.punctuation }, 1)
                )
                "yaml", "yml" -> listOf(
                    Rule(Pattern.compile("#.*"), { it.comment }, 0),
                    Rule(Pattern.compile("(\"(?:\\\\.|[^\"\\\\])*\"|'(?:\\\\.|[^'\\\\])*')"), { it.string }),
                    Rule(Pattern.compile("\\b([0-9]+(?:\\.[0-9]+)?)\\b"), { it.number }),
                    Rule(Pattern.compile("\\b(true|false|null|yes|no|on|off)\\b"), { it.keyword }),
                    Rule(Pattern.compile("^\\s*([a-zA-Z_][a-zA-Z0-9_]*)(?=\\s*:)"), { it.attribute }),
                    Rule(Pattern.compile("([:\\-|>])"), { it.punctuation })
                )
                // Add other languages here...
                else -> listOf( // Default fallback for plain text
                    Rule(Pattern.compile("."), { it.variable }, 0)
                )
            }
        }
    }

    fun highlightToAnnotatedString(
        code: String,
        language: String?,
        theme: SyntaxTheme = CatppuccinLatteTheme
    ): AnnotatedString {
        if (code.isEmpty()) return AnnotatedString(code)

        return try {
            val rules = getRules(language, theme)
            buildAnnotatedString {
                append(code) // Append the raw code first

                // Use a list of ranges for more efficient overlap checking
                val appliedRanges = mutableListOf<IntRange>()

                rules.forEach { rule ->
                    val matcher = rule.pattern.matcher(code)
                    while (matcher.find()) {
                        val targetGroup = rule.groupIndex
                        if (targetGroup > matcher.groupCount()) continue

                        val start = matcher.start(targetGroup)
                        val end = matcher.end(targetGroup)

                        if (start == -1 || start >= end) continue

                        val currentRange = start until end
                        val isOverlapping = appliedRanges.any { it.first < currentRange.last && it.last > currentRange.first }

                        if (!isOverlapping) {
                            addStyle(SpanStyle(color = rule.color(theme)), start, end)
                            appliedRanges.add(currentRange)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Graceful fallback in case of a regex error or other issue
            AnnotatedString(code)
        }
    }
}