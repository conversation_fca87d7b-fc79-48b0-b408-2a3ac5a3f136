{"logs": [{"outputFile": "com.example.everytalk.app-mergeBenchmarkResources-51:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,9009,9099", "endColumns": "89,89,91", "endOffsets": "190,9094,9186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,297,385,479,578,664,741,833,925,1010,1091,1177,1250,1341,1418,1497,1574,1654,1724", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "292,380,474,573,659,736,828,920,1005,1086,1172,1245,1336,1413,1492,1569,1649,1719,1837"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1021,1109,1203,1302,1388,1465,7899,7991,8076,8157,8243,8316,8407,8484,8563,8741,8821,8891", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "1016,1104,1198,1297,1383,1460,1552,7986,8071,8152,8238,8311,8402,8479,8558,8635,8816,8886,9004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1557,1676,1796,1917,2037,2136,2234,2349,2494,2614,2752,2837,2937,3030,3128,3245,3372,3477,3612,3746,3887,4057,4192,4315,4442,4570,4664,4762,4883,5011,5108,5211,5320,5459,5604,5713,5813,5898,5991,6086,6213,6307,6398,6507,6595,6678,6775,6879,6972,7069,7157,7265,7362,7464,7602,7692,7800", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "1671,1791,1912,2032,2131,2229,2344,2489,2609,2747,2832,2932,3025,3123,3240,3367,3472,3607,3741,3882,4052,4187,4310,4437,4565,4659,4757,4878,5006,5103,5206,5315,5454,5599,5708,5808,5893,5986,6081,6208,6302,6393,6502,6590,6673,6770,6874,6967,7064,7152,7260,7357,7459,7597,7687,7795,7894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "195,293,395,493,597,701,803,8640", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "288,390,488,592,696,798,915,8736"}}]}]}