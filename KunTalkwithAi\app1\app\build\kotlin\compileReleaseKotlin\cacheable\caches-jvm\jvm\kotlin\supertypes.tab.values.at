/ Header Record For PersistentHashMapValueStorage3 2kotlinx.serialization.internal.GeneratedSerializer4 3com.example.everytalk.data.DataClass.ApiContentPart3 2kotlinx.serialization.internal.GeneratedSerializer4 3com.example.everytalk.data.DataClass.ApiContentPart3 2kotlinx.serialization.internal.GeneratedSerializer4 3com.example.everytalk.data.DataClass.ApiContentPart3 2kotlinx.serialization.internal.GeneratedSerializer. -com.example.everytalk.data.DataClass.IMessage8 7com.example.everytalk.data.DataClass.AbstractApiMessage3 2kotlinx.serialization.internal.GeneratedSerializer8 7com.example.everytalk.data.DataClass.AbstractApiMessage3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer1 0com.example.everytalk.data.DataClass.ContentPart1 0com.example.everytalk.data.DataClass.ContentPart1 0com.example.everytalk.data.DataClass.ContentPart3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer* )com.example.everytalk.data.DataClass.Part3 2kotlinx.serialization.internal.GeneratedSerializer* )com.example.everytalk.data.DataClass.Part3 2kotlinx.serialization.internal.GeneratedSerializer* )com.example.everytalk.data.DataClass.Part3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum. -com.example.everytalk.data.DataClass.IMessage3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer" !kotlinx.serialization.KSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer< ;kotlinx.serialization.json.JsonContentPolymorphicSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer2 1com.example.everytalk.data.network.AppStreamEvent3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum/ .com.example.everytalk.models.SelectedMediaItem3 2kotlinx.serialization.internal.GeneratedSerializer/ .com.example.everytalk.models.SelectedMediaItem3 2kotlinx.serialization.internal.GeneratedSerializer/ .com.example.everytalk.models.SelectedMediaItem3 2kotlinx.serialization.internal.GeneratedSerializer/ .com.example.everytalk.models.SelectedMediaItem3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.util.LinkedHashMap$ #androidx.lifecycle.AndroidViewModel3 2kotlinx.serialization.internal.GeneratedSerializer- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment kotlin.Enum> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItem> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItem> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItem> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItem> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItem> =com.example.everytalk.ui.screens.MainScreen.chat.ChatListItemE Dcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleStateE Dcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleState" !kotlinx.serialization.KSerializer) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock) (com.example.everytalk.util.MarkdownBlock' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser' &com.example.everytalk.util.BlockParser) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement kotlin.Enum& %com.example.everytalk.util.RenderMode& %com.example.everytalk.util.RenderMode0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult" !kotlinx.serialization.KSerializer" !kotlinx.serialization.KSerializer- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment= <com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment kotlin.EnumE Dcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleStateE Dcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleState) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement) (com.example.everytalk.util.InlineElement kotlin.Enum& %com.example.everytalk.util.RenderMode& %com.example.everytalk.util.RenderMode0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult0 /com.example.everytalk.util.ProcessedEventResult