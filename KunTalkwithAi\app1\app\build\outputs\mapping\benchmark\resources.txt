Marking id:view_tree_lifecycle_owner:2131034204 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:view_tree_view_model_store_owner:2131034207 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_bottom_sheet_collapse_description:2131296278 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_bottom_sheet_dismiss_description:2131296279 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_bottom_sheet_expand_description:2131296281 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:view_tree_on_back_pressed_dispatcher_owner:2131034205 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:code_copied:2131296270 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:default_popup_window_title:2131296274 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:view_tree_saved_state_registry_owner:2131034206 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:compose_view_saveable_id_tag:2131034156 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_0:2131034113 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_1:2131034114 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_2:2131034125 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_3:2131034136 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_4:2131034139 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_5:2131034140 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_6:2131034141 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_7:2131034142 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_8:2131034143 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_9:2131034144 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_10:2131034115 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_11:2131034116 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_12:2131034117 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_13:2131034118 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_14:2131034119 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_15:2131034120 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_16:2131034121 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_17:2131034122 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_18:2131034123 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_19:2131034124 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_20:2131034126 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_21:2131034127 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_22:2131034128 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_23:2131034129 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_24:2131034130 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_25:2131034131 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_26:2131034132 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_27:2131034133 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_28:2131034134 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_29:2131034135 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_30:2131034137 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_custom_action_31:2131034138 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_bottom_sheet_pane_title:2131296282 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:close_sheet:2131296269 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:ai_reply_message:2131296256 reachable: referenced from in_memory_r8_base_classes0.dex
Marking drawable:ic_launcher_foreground:2130968585 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_screen_reader_focusable:2131034192 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_accessibility_heading:2131034186 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_accessibility_pane_title:2131034187 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_state_description:2131034193 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_accessibility_actions:2131034184 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:accessibility_action_clickable_span:2131034112 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_accessibility_clickable_spans:2131034185 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:wrapped_composition_tag:2131034209 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_window_insets_animation_callback:2131034198 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_on_apply_window_listener:2131034189 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_compat_insets_dispatch:2131034188 reachable: referenced from in_memory_r8_base_classes0.dex
Marking drawable:ic_foreground_logo:2130968583 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:androidx_compose_ui_view_composition_context:2131034150 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:hide_in_inspector_tag:2131034163 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:navigation_menu:2131296335 reachable: referenced from in_memory_r8_base_classes0.dex
Marking attr:alpha:2130771971 reachable: referenced from in_memory_r8_base_classes0.dex
Marking attr:lStar:2130771995 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:inspection_slot_table_set:2131034167 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_unhandled_key_event_manager:2131034196 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:tag_unhandled_key_listeners:2131034197 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:is_pooling_container_tag:2131034168 reachable: referenced from in_memory_r8_base_classes0.dex
Marking style:EdgeToEdgeFloatingDialogWindowTheme:2131361794 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:hide_graphics_layer_in_inspector_tag:2131034161 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:view_tree_disjoint_parent:2131034203 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:default_error_message:2131296273 reachable: referenced from in_memory_r8_base_classes0.dex
Marking style:DialogWindowTheme:2131361792 reachable: referenced from in_memory_r8_base_classes0.dex
Marking style:FloatingDialogWindowTheme:2131361796 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_dropdown_menu_expanded:2131296316 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_dropdown_menu_collapsed:2131296315 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_dropdown_menu_toggle:2131296317 reachable: referenced from in_memory_r8_base_classes0.dex
Marking drawable:abc_vector_test:2130968576 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:view_sources:2131296352 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:androidx_startup:2131296257 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:connecting_to_model:2131296271 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:tab:2131296348 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:switch_role:2131296347 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:pooling_container_listener_holder_tag:2131034179 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:report_drawn:2131034180 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:copy:2131296272 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:indeterminate:2131296277 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:state_off:2131296344 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:state_on:2131296345 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:selected:2131296341 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:not_selected:2131296338 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:template_percent:2131296349 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:in_progress:2131296276 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:state_empty:2131296343 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:cannot_open_link:2131296267 reachable: referenced from in_memory_r8_base_classes0.dex
Marking id:consume_window_insets_tag:2131034157 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_dialog:2131296314 reachable: referenced from in_memory_r8_base_classes0.dex
Marking string:m3c_bottom_sheet_drag_handle_description:2131296280 reachable: referenced from in_memory_r8_base_classes0.dex
@com.example.everytalk:attr/action : reachable=false
@com.example.everytalk:attr/activityAction : reachable=false
@com.example.everytalk:attr/activityName : reachable=false
@com.example.everytalk:attr/alpha : reachable=true
@com.example.everytalk:attr/alwaysExpand : reachable=false
@com.example.everytalk:attr/argType : reachable=false
@com.example.everytalk:attr/clearTop : reachable=false
@com.example.everytalk:attr/data : reachable=false
@com.example.everytalk:attr/dataPattern : reachable=false
@com.example.everytalk:attr/destination : reachable=false
@com.example.everytalk:attr/enterAnim : reachable=false
@com.example.everytalk:attr/exitAnim : reachable=false
@com.example.everytalk:attr/finishPrimaryWithSecondary : reachable=false
@com.example.everytalk:attr/finishSecondaryWithPrimary : reachable=false
@com.example.everytalk:attr/font : reachable=false
@com.example.everytalk:attr/fontProviderAuthority : reachable=false
@com.example.everytalk:attr/fontProviderCerts : reachable=false
@com.example.everytalk:attr/fontProviderFallbackQuery : reachable=false
@com.example.everytalk:attr/fontProviderFetchStrategy : reachable=false
@com.example.everytalk:attr/fontProviderFetchTimeout : reachable=false
@com.example.everytalk:attr/fontProviderPackage : reachable=false
@com.example.everytalk:attr/fontProviderQuery : reachable=false
@com.example.everytalk:attr/fontProviderSystemFontFamily : reachable=false
@com.example.everytalk:attr/fontStyle : reachable=false
@com.example.everytalk:attr/fontVariationSettings : reachable=false
@com.example.everytalk:attr/fontWeight : reachable=false
@com.example.everytalk:attr/graph : reachable=false
@com.example.everytalk:attr/lStar : reachable=true
@com.example.everytalk:attr/launchSingleTop : reachable=false
@com.example.everytalk:attr/mimeType : reachable=false
@com.example.everytalk:attr/navGraph : reachable=false
@com.example.everytalk:attr/nestedScrollViewStyle : reachable=false
@com.example.everytalk:attr/nullable : reachable=false
@com.example.everytalk:attr/placeholderActivityName : reachable=false
@com.example.everytalk:attr/popEnterAnim : reachable=false
@com.example.everytalk:attr/popExitAnim : reachable=false
@com.example.everytalk:attr/popUpTo : reachable=false
@com.example.everytalk:attr/popUpToInclusive : reachable=false
@com.example.everytalk:attr/popUpToSaveState : reachable=false
@com.example.everytalk:attr/primaryActivityName : reachable=false
@com.example.everytalk:attr/queryPatterns : reachable=false
@com.example.everytalk:attr/restoreState : reachable=false
@com.example.everytalk:attr/route : reachable=false
@com.example.everytalk:attr/secondaryActivityAction : reachable=false
@com.example.everytalk:attr/secondaryActivityName : reachable=false
@com.example.everytalk:attr/shortcutMatchRequired : reachable=false
@com.example.everytalk:attr/splitLayoutDirection : reachable=false
@com.example.everytalk:attr/splitMinSmallestWidth : reachable=false
@com.example.everytalk:attr/splitMinWidth : reachable=false
@com.example.everytalk:attr/splitRatio : reachable=false
@com.example.everytalk:attr/startDestination : reachable=false
@com.example.everytalk:attr/targetPackage : reachable=false
@com.example.everytalk:attr/ttcIndex : reachable=false
@com.example.everytalk:attr/uri : reachable=false
@com.example.everytalk:color/androidx_core_ripple_material_light : reachable=false
@com.example.everytalk:color/androidx_core_secondary_text_default_material_light : reachable=false
@com.example.everytalk:color/black : reachable=false
@com.example.everytalk:color/call_notification_answer_color : reachable=false
@com.example.everytalk:color/call_notification_decline_color : reachable=false
@com.example.everytalk:color/ic_launcher_background : reachable=false
@com.example.everytalk:color/ktalk : reachable=false
@com.example.everytalk:color/notification_action_color_filter : reachable=false
    @com.example.everytalk:color/androidx_core_secondary_text_default_material_light
@com.example.everytalk:color/notification_icon_bg_color : reachable=false
@com.example.everytalk:color/purple_200 : reachable=false
@com.example.everytalk:color/purple_500 : reachable=false
@com.example.everytalk:color/purple_700 : reachable=false
@com.example.everytalk:color/teal_200 : reachable=false
@com.example.everytalk:color/teal_700 : reachable=false
@com.example.everytalk:color/vector_tint_color : reachable=false
@com.example.everytalk:color/vector_tint_theme_color : reachable=false
@com.example.everytalk:color/white : reachable=false
@com.example.everytalk:dimen/compat_button_inset_horizontal_material : reachable=false
@com.example.everytalk:dimen/compat_button_inset_vertical_material : reachable=false
@com.example.everytalk:dimen/compat_button_padding_horizontal_material : reachable=false
@com.example.everytalk:dimen/compat_button_padding_vertical_material : reachable=false
@com.example.everytalk:dimen/compat_control_corner_material : reachable=false
@com.example.everytalk:dimen/compat_notification_large_icon_max_height : reachable=false
@com.example.everytalk:dimen/compat_notification_large_icon_max_width : reachable=false
@com.example.everytalk:dimen/notification_action_icon_size : reachable=false
@com.example.everytalk:dimen/notification_action_text_size : reachable=false
@com.example.everytalk:dimen/notification_big_circle_margin : reachable=false
@com.example.everytalk:dimen/notification_content_margin_start : reachable=false
@com.example.everytalk:dimen/notification_large_icon_height : reachable=false
@com.example.everytalk:dimen/notification_large_icon_width : reachable=false
@com.example.everytalk:dimen/notification_main_column_padding_top : reachable=false
@com.example.everytalk:dimen/notification_media_narrow_margin : reachable=false
@com.example.everytalk:dimen/notification_right_icon_size : reachable=false
@com.example.everytalk:dimen/notification_right_side_padding_top : reachable=false
@com.example.everytalk:dimen/notification_small_icon_background_padding : reachable=false
@com.example.everytalk:dimen/notification_small_icon_size_as_large : reachable=false
@com.example.everytalk:dimen/notification_subtext_size : reachable=false
@com.example.everytalk:dimen/notification_top_pad : reachable=false
@com.example.everytalk:dimen/notification_top_pad_large_text : reachable=false
@com.example.everytalk:drawable/abc_vector_test : reachable=true
@com.example.everytalk:drawable/ic_call_answer : reachable=false
@com.example.everytalk:drawable/ic_call_answer_low : reachable=false
@com.example.everytalk:drawable/ic_call_answer_video : reachable=false
@com.example.everytalk:drawable/ic_call_answer_video_low : reachable=false
@com.example.everytalk:drawable/ic_call_decline : reachable=false
@com.example.everytalk:drawable/ic_call_decline_low : reachable=false
@com.example.everytalk:drawable/ic_foreground_logo : reachable=true
@com.example.everytalk:drawable/ic_launcher_background : reachable=false
@com.example.everytalk:drawable/ic_launcher_foreground : reachable=true
    @com.example.everytalk:drawable/ic_foreground_logo
@com.example.everytalk:drawable/notification_action_background : reachable=false
    @com.example.everytalk:color/androidx_core_ripple_material_light
    @com.example.everytalk:dimen/compat_button_inset_horizontal_material
    @com.example.everytalk:dimen/compat_button_inset_vertical_material
    @com.example.everytalk:dimen/compat_control_corner_material
    @com.example.everytalk:dimen/compat_button_padding_vertical_material
    @com.example.everytalk:dimen/compat_button_padding_horizontal_material
@com.example.everytalk:drawable/notification_bg : reachable=false
    @com.example.everytalk:drawable/notification_bg_normal_pressed
    @com.example.everytalk:drawable/notification_bg_normal
@com.example.everytalk:drawable/notification_bg_low : reachable=false
    @com.example.everytalk:drawable/notification_bg_low_pressed
    @com.example.everytalk:drawable/notification_bg_low_normal
@com.example.everytalk:drawable/notification_bg_low_normal : reachable=false
@com.example.everytalk:drawable/notification_bg_low_pressed : reachable=false
@com.example.everytalk:drawable/notification_bg_normal : reachable=false
@com.example.everytalk:drawable/notification_bg_normal_pressed : reachable=false
@com.example.everytalk:drawable/notification_icon_background : reachable=false
    @com.example.everytalk:color/notification_icon_bg_color
@com.example.everytalk:drawable/notification_oversize_large_icon_bg : reachable=false
@com.example.everytalk:drawable/notification_template_icon_bg : reachable=false
@com.example.everytalk:drawable/notification_template_icon_low_bg : reachable=false
@com.example.everytalk:drawable/notification_tile_bg : reachable=false
    @com.example.everytalk:drawable/notify_panel_notification_icon_bg
@com.example.everytalk:drawable/notify_panel_notification_icon_bg : reachable=false
@com.example.everytalk:id/accessibility_action_clickable_span : reachable=true
@com.example.everytalk:id/accessibility_custom_action_0 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_1 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_10 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_11 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_12 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_13 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_14 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_15 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_16 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_17 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_18 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_19 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_2 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_20 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_21 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_22 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_23 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_24 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_25 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_26 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_27 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_28 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_29 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_3 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_30 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_31 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_4 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_5 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_6 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_7 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_8 : reachable=true
@com.example.everytalk:id/accessibility_custom_action_9 : reachable=true
@com.example.everytalk:id/action_container : reachable=false
@com.example.everytalk:id/action_divider : reachable=false
@com.example.everytalk:id/action_image : reachable=false
@com.example.everytalk:id/action_text : reachable=false
@com.example.everytalk:id/actions : reachable=false
@com.example.everytalk:id/androidx_compose_ui_view_composition_context : reachable=true
@com.example.everytalk:id/androidx_window_activity_scope : reachable=false
@com.example.everytalk:id/async : reachable=false
@com.example.everytalk:id/blocking : reachable=false
@com.example.everytalk:id/chronometer : reachable=false
@com.example.everytalk:id/coil3_request_manager : reachable=false
@com.example.everytalk:id/compose_view_saveable_id_tag : reachable=true
@com.example.everytalk:id/consume_window_insets_tag : reachable=true
@com.example.everytalk:id/dialog_button : reachable=false
@com.example.everytalk:id/edit_text_id : reachable=false
@com.example.everytalk:id/forever : reachable=false
@com.example.everytalk:id/hide_graphics_layer_in_inspector_tag : reachable=true
@com.example.everytalk:id/hide_ime_id : reachable=false
@com.example.everytalk:id/hide_in_inspector_tag : reachable=true
@com.example.everytalk:id/icon : reachable=false
@com.example.everytalk:id/icon_group : reachable=false
@com.example.everytalk:id/info : reachable=false
@com.example.everytalk:id/inspection_slot_table_set : reachable=true
@com.example.everytalk:id/is_pooling_container_tag : reachable=true
@com.example.everytalk:id/italic : reachable=false
@com.example.everytalk:id/line1 : reachable=false
@com.example.everytalk:id/line3 : reachable=false
@com.example.everytalk:id/locale : reachable=false
@com.example.everytalk:id/ltr : reachable=false
@com.example.everytalk:id/nav_controller_view_tag : reachable=false
@com.example.everytalk:id/normal : reachable=false
@com.example.everytalk:id/notification_background : reachable=false
@com.example.everytalk:id/notification_main_column : reachable=false
@com.example.everytalk:id/notification_main_column_container : reachable=false
@com.example.everytalk:id/pooling_container_listener_holder_tag : reachable=true
@com.example.everytalk:id/report_drawn : reachable=true
@com.example.everytalk:id/right_icon : reachable=false
@com.example.everytalk:id/right_side : reachable=false
@com.example.everytalk:id/rtl : reachable=false
@com.example.everytalk:id/tag_accessibility_actions : reachable=true
@com.example.everytalk:id/tag_accessibility_clickable_spans : reachable=true
@com.example.everytalk:id/tag_accessibility_heading : reachable=true
@com.example.everytalk:id/tag_accessibility_pane_title : reachable=true
@com.example.everytalk:id/tag_compat_insets_dispatch : reachable=true
@com.example.everytalk:id/tag_on_apply_window_listener : reachable=true
@com.example.everytalk:id/tag_on_receive_content_listener : reachable=false
@com.example.everytalk:id/tag_on_receive_content_mime_types : reachable=false
@com.example.everytalk:id/tag_screen_reader_focusable : reachable=true
@com.example.everytalk:id/tag_state_description : reachable=true
@com.example.everytalk:id/tag_system_bar_state_monitor : reachable=false
@com.example.everytalk:id/tag_transition_group : reachable=false
@com.example.everytalk:id/tag_unhandled_key_event_manager : reachable=true
@com.example.everytalk:id/tag_unhandled_key_listeners : reachable=true
@com.example.everytalk:id/tag_window_insets_animation_callback : reachable=true
@com.example.everytalk:id/text : reachable=false
@com.example.everytalk:id/text2 : reachable=false
@com.example.everytalk:id/time : reachable=false
@com.example.everytalk:id/title : reachable=false
@com.example.everytalk:id/view_tree_disjoint_parent : reachable=true
@com.example.everytalk:id/view_tree_lifecycle_owner : reachable=true
@com.example.everytalk:id/view_tree_on_back_pressed_dispatcher_owner : reachable=true
@com.example.everytalk:id/view_tree_saved_state_registry_owner : reachable=true
@com.example.everytalk:id/view_tree_view_model_store_owner : reachable=true
@com.example.everytalk:id/webview_template_tag_key : reachable=false
@com.example.everytalk:id/wrapped_composition_tag : reachable=true
@com.example.everytalk:integer/m3c_window_layout_in_display_cutout_mode : reachable=false
@com.example.everytalk:integer/status_bar_notification_info_maxnum : reachable=false
@com.example.everytalk:layout/custom_dialog : reachable=false
@com.example.everytalk:layout/ime_base_split_test_activity : reachable=false
@com.example.everytalk:layout/ime_secondary_split_test_activity : reachable=false
@com.example.everytalk:layout/notification_action : reachable=false
    @com.example.everytalk:style/Widget_Compat_NotificationActionContainer
    @com.example.everytalk:dimen/notification_action_icon_size
    @com.example.everytalk:style/Widget_Compat_NotificationActionText
@com.example.everytalk:layout/notification_action_tombstone : reachable=false
    @com.example.everytalk:style/Widget_Compat_NotificationActionContainer
    @com.example.everytalk:dimen/notification_action_icon_size
    @com.example.everytalk:style/Widget_Compat_NotificationActionText
@com.example.everytalk:layout/notification_template_custom_big : reachable=false
    @com.example.everytalk:layout/notification_template_icon_group
    @com.example.everytalk:dimen/notification_large_icon_width
    @com.example.everytalk:dimen/notification_large_icon_height
    @com.example.everytalk:dimen/notification_right_side_padding_top
    @com.example.everytalk:layout/notification_template_part_time
    @com.example.everytalk:layout/notification_template_part_chronometer
    @com.example.everytalk:style/TextAppearance_Compat_Notification_Info
@com.example.everytalk:layout/notification_template_icon_group : reachable=false
    @com.example.everytalk:dimen/notification_large_icon_width
    @com.example.everytalk:dimen/notification_large_icon_height
    @com.example.everytalk:dimen/notification_big_circle_margin
    @com.example.everytalk:dimen/notification_right_icon_size
@com.example.everytalk:layout/notification_template_part_chronometer : reachable=false
    @com.example.everytalk:style/TextAppearance_Compat_Notification_Time
@com.example.everytalk:layout/notification_template_part_time : reachable=false
    @com.example.everytalk:style/TextAppearance_Compat_Notification_Time
@com.example.everytalk:mipmap/ic_launcher : reachable=true
    @com.example.everytalk:color/ic_launcher_background
    @com.example.everytalk:drawable/ic_launcher_foreground
@com.example.everytalk:mipmap/ic_launcher_foreground : reachable=false
@com.example.everytalk:mipmap/ic_launcher_round : reachable=true
    @com.example.everytalk:color/ic_launcher_background
    @com.example.everytalk:drawable/ic_launcher_foreground
@com.example.everytalk:mipmap/kztalk : reachable=false
@com.example.everytalk:string/ai_reply_message : reachable=true
@com.example.everytalk:string/androidx_startup : reachable=true
@com.example.everytalk:string/app_name : reachable=true
@com.example.everytalk:string/autofill : reachable=false
@com.example.everytalk:string/call_notification_answer_action : reachable=false
@com.example.everytalk:string/call_notification_answer_video_action : reachable=false
@com.example.everytalk:string/call_notification_decline_action : reachable=false
@com.example.everytalk:string/call_notification_hang_up_action : reachable=false
@com.example.everytalk:string/call_notification_incoming_text : reachable=false
@com.example.everytalk:string/call_notification_ongoing_text : reachable=false
@com.example.everytalk:string/call_notification_screening_text : reachable=false
@com.example.everytalk:string/cannot_open_link : reachable=true
@com.example.everytalk:string/close_drawer : reachable=false
@com.example.everytalk:string/close_sheet : reachable=true
@com.example.everytalk:string/code_copied : reachable=true
@com.example.everytalk:string/connecting_to_model : reachable=true
@com.example.everytalk:string/copy : reachable=true
@com.example.everytalk:string/default_error_message : reachable=true
@com.example.everytalk:string/default_popup_window_title : reachable=true
@com.example.everytalk:string/dropdown_menu : reachable=false
@com.example.everytalk:string/in_progress : reachable=true
@com.example.everytalk:string/indeterminate : reachable=true
@com.example.everytalk:string/m3c_bottom_sheet_collapse_description : reachable=true
@com.example.everytalk:string/m3c_bottom_sheet_dismiss_description : reachable=true
@com.example.everytalk:string/m3c_bottom_sheet_drag_handle_description : reachable=true
@com.example.everytalk:string/m3c_bottom_sheet_expand_description : reachable=true
@com.example.everytalk:string/m3c_bottom_sheet_pane_title : reachable=true
@com.example.everytalk:string/m3c_date_input_headline : reachable=false
@com.example.everytalk:string/m3c_date_input_headline_description : reachable=false
@com.example.everytalk:string/m3c_date_input_invalid_for_pattern : reachable=false
@com.example.everytalk:string/m3c_date_input_invalid_not_allowed : reachable=false
@com.example.everytalk:string/m3c_date_input_invalid_year_range : reachable=false
@com.example.everytalk:string/m3c_date_input_label : reachable=false
@com.example.everytalk:string/m3c_date_input_no_input_description : reachable=false
@com.example.everytalk:string/m3c_date_input_title : reachable=false
@com.example.everytalk:string/m3c_date_picker_headline : reachable=false
@com.example.everytalk:string/m3c_date_picker_headline_description : reachable=false
@com.example.everytalk:string/m3c_date_picker_navigate_to_year_description : reachable=false
@com.example.everytalk:string/m3c_date_picker_no_selection_description : reachable=false
@com.example.everytalk:string/m3c_date_picker_scroll_to_earlier_years : reachable=false
@com.example.everytalk:string/m3c_date_picker_scroll_to_later_years : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_calendar_mode : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_day_selection : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_input_mode : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_next_month : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_previous_month : reachable=false
@com.example.everytalk:string/m3c_date_picker_switch_to_year_selection : reachable=false
@com.example.everytalk:string/m3c_date_picker_title : reachable=false
@com.example.everytalk:string/m3c_date_picker_today_description : reachable=false
@com.example.everytalk:string/m3c_date_picker_year_picker_pane_title : reachable=false
@com.example.everytalk:string/m3c_date_range_input_invalid_range_input : reachable=false
@com.example.everytalk:string/m3c_date_range_input_title : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_day_in_range : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_end_headline : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_scroll_to_next_month : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_scroll_to_previous_month : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_start_headline : reachable=false
@com.example.everytalk:string/m3c_date_range_picker_title : reachable=false
@com.example.everytalk:string/m3c_dialog : reachable=true
@com.example.everytalk:string/m3c_dropdown_menu_collapsed : reachable=true
@com.example.everytalk:string/m3c_dropdown_menu_expanded : reachable=true
@com.example.everytalk:string/m3c_dropdown_menu_toggle : reachable=true
@com.example.everytalk:string/m3c_search_bar_search : reachable=false
@com.example.everytalk:string/m3c_snackbar_dismiss : reachable=false
@com.example.everytalk:string/m3c_suggestions_available : reachable=false
@com.example.everytalk:string/m3c_time_picker_am : reachable=false
@com.example.everytalk:string/m3c_time_picker_hour : reachable=false
@com.example.everytalk:string/m3c_time_picker_hour_24h_suffix : reachable=false
@com.example.everytalk:string/m3c_time_picker_hour_selection : reachable=false
@com.example.everytalk:string/m3c_time_picker_hour_suffix : reachable=false
@com.example.everytalk:string/m3c_time_picker_hour_text_field : reachable=false
@com.example.everytalk:string/m3c_time_picker_minute : reachable=false
@com.example.everytalk:string/m3c_time_picker_minute_selection : reachable=false
@com.example.everytalk:string/m3c_time_picker_minute_suffix : reachable=false
@com.example.everytalk:string/m3c_time_picker_minute_text_field : reachable=false
@com.example.everytalk:string/m3c_time_picker_period_toggle_description : reachable=false
@com.example.everytalk:string/m3c_time_picker_pm : reachable=false
@com.example.everytalk:string/m3c_tooltip_long_press_label : reachable=false
@com.example.everytalk:string/m3c_tooltip_pane_description : reachable=false
@com.example.everytalk:string/navigation_menu : reachable=true
@com.example.everytalk:string/no_app_found_for_link : reachable=false
@com.example.everytalk:string/no_permission_open_link : reachable=false
@com.example.everytalk:string/not_selected : reachable=true
@com.example.everytalk:string/range_end : reachable=false
@com.example.everytalk:string/range_start : reachable=false
@com.example.everytalk:string/selected : reachable=true
@com.example.everytalk:string/snackbar_pane_title : reachable=false
@com.example.everytalk:string/state_empty : reachable=true
@com.example.everytalk:string/state_off : reachable=true
@com.example.everytalk:string/state_on : reachable=true
@com.example.everytalk:string/status_bar_notification_info_overflow : reachable=false
@com.example.everytalk:string/switch_role : reachable=true
@com.example.everytalk:string/tab : reachable=true
@com.example.everytalk:string/template_percent : reachable=true
@com.example.everytalk:string/tooltip_description : reachable=false
@com.example.everytalk:string/tooltip_label : reachable=false
@com.example.everytalk:string/view_sources : reachable=true
@com.example.everytalk:style/DialogWindowTheme : reachable=true
@com.example.everytalk:style/EdgeToEdgeFloatingDialogTheme : reachable=false
    @com.example.everytalk:integer/m3c_window_layout_in_display_cutout_mode
@com.example.everytalk:style/EdgeToEdgeFloatingDialogWindowTheme : reachable=true
    @com.example.everytalk:style/EdgeToEdgeFloatingDialogTheme
@com.example.everytalk:style/FloatingDialogTheme : reachable=false
@com.example.everytalk:style/FloatingDialogWindowTheme : reachable=true
    @com.example.everytalk:style/FloatingDialogTheme
@com.example.everytalk:style/TextAppearance_Compat_Notification : reachable=false
@com.example.everytalk:style/TextAppearance_Compat_Notification_Info : reachable=false
@com.example.everytalk:style/TextAppearance_Compat_Notification_Line2 : reachable=false
    @com.example.everytalk:style/TextAppearance_Compat_Notification_Info
@com.example.everytalk:style/TextAppearance_Compat_Notification_Time : reachable=false
@com.example.everytalk:style/TextAppearance_Compat_Notification_Title : reachable=false
@com.example.everytalk:style/Theme_App1 : reachable=true
@com.example.everytalk:style/Widget_Compat_NotificationActionContainer : reachable=false
    @com.example.everytalk:drawable/notification_action_background
@com.example.everytalk:style/Widget_Compat_NotificationActionText : reachable=false
    @com.example.everytalk:color/androidx_core_secondary_text_default_material_light
    @com.example.everytalk:dimen/notification_action_text_size
@com.example.everytalk:xml/backup_rules : reachable=true
@com.example.everytalk:xml/data_extraction_rules : reachable=true
@com.example.everytalk:xml/file_paths : reachable=true
@com.example.everytalk:xml/network_security_config : reachable=true

The root reachable resources are:
 attr:alpha:2130771971
 attr:lStar:2130771995
 drawable:abc_vector_test:2130968576
 drawable:ic_foreground_logo:2130968583
 drawable:ic_launcher_foreground:2130968585
 id:accessibility_action_clickable_span:2131034112
 id:accessibility_custom_action_0:2131034113
 id:accessibility_custom_action_1:2131034114
 id:accessibility_custom_action_10:2131034115
 id:accessibility_custom_action_11:2131034116
 id:accessibility_custom_action_12:2131034117
 id:accessibility_custom_action_13:2131034118
 id:accessibility_custom_action_14:2131034119
 id:accessibility_custom_action_15:2131034120
 id:accessibility_custom_action_16:2131034121
 id:accessibility_custom_action_17:2131034122
 id:accessibility_custom_action_18:2131034123
 id:accessibility_custom_action_19:2131034124
 id:accessibility_custom_action_2:2131034125
 id:accessibility_custom_action_20:2131034126
 id:accessibility_custom_action_21:2131034127
 id:accessibility_custom_action_22:2131034128
 id:accessibility_custom_action_23:2131034129
 id:accessibility_custom_action_24:2131034130
 id:accessibility_custom_action_25:2131034131
 id:accessibility_custom_action_26:2131034132
 id:accessibility_custom_action_27:2131034133
 id:accessibility_custom_action_28:2131034134
 id:accessibility_custom_action_29:2131034135
 id:accessibility_custom_action_3:2131034136
 id:accessibility_custom_action_30:2131034137
 id:accessibility_custom_action_31:2131034138
 id:accessibility_custom_action_4:2131034139
 id:accessibility_custom_action_5:2131034140
 id:accessibility_custom_action_6:2131034141
 id:accessibility_custom_action_7:2131034142
 id:accessibility_custom_action_8:2131034143
 id:accessibility_custom_action_9:2131034144
 id:androidx_compose_ui_view_composition_context:2131034150
 id:compose_view_saveable_id_tag:2131034156
 id:consume_window_insets_tag:2131034157
 id:hide_graphics_layer_in_inspector_tag:2131034161
 id:hide_in_inspector_tag:2131034163
 id:inspection_slot_table_set:2131034167
 id:is_pooling_container_tag:2131034168
 id:pooling_container_listener_holder_tag:2131034179
 id:report_drawn:2131034180
 id:tag_accessibility_actions:2131034184
 id:tag_accessibility_clickable_spans:2131034185
 id:tag_accessibility_heading:2131034186
 id:tag_accessibility_pane_title:2131034187
 id:tag_compat_insets_dispatch:2131034188
 id:tag_on_apply_window_listener:2131034189
 id:tag_screen_reader_focusable:2131034192
 id:tag_state_description:2131034193
 id:tag_unhandled_key_event_manager:2131034196
 id:tag_unhandled_key_listeners:2131034197
 id:tag_window_insets_animation_callback:2131034198
 id:view_tree_disjoint_parent:2131034203
 id:view_tree_lifecycle_owner:2131034204
 id:view_tree_on_back_pressed_dispatcher_owner:2131034205
 id:view_tree_saved_state_registry_owner:2131034206
 id:view_tree_view_model_store_owner:2131034207
 id:wrapped_composition_tag:2131034209
 mipmap:ic_launcher:2131230720
 mipmap:ic_launcher_round:2131230722
 string:ai_reply_message:2131296256
 string:androidx_startup:2131296257
 string:app_name:2131296258
 string:cannot_open_link:2131296267
 string:close_sheet:2131296269
 string:code_copied:2131296270
 string:connecting_to_model:2131296271
 string:copy:2131296272
 string:default_error_message:2131296273
 string:default_popup_window_title:2131296274
 string:in_progress:2131296276
 string:indeterminate:2131296277
 string:m3c_bottom_sheet_collapse_description:2131296278
 string:m3c_bottom_sheet_dismiss_description:2131296279
 string:m3c_bottom_sheet_drag_handle_description:2131296280
 string:m3c_bottom_sheet_expand_description:2131296281
 string:m3c_bottom_sheet_pane_title:2131296282
 string:m3c_dialog:2131296314
 string:m3c_dropdown_menu_collapsed:2131296315
 string:m3c_dropdown_menu_expanded:2131296316
 string:m3c_dropdown_menu_toggle:2131296317
 string:navigation_menu:2131296335
 string:not_selected:2131296338
 string:selected:2131296341
 string:state_empty:2131296343
 string:state_off:2131296344
 string:state_on:2131296345
 string:switch_role:2131296347
 string:tab:2131296348
 string:template_percent:2131296349
 string:view_sources:2131296352
 style:DialogWindowTheme:2131361792
 style:EdgeToEdgeFloatingDialogWindowTheme:2131361794
 style:FloatingDialogWindowTheme:2131361796
 style:Theme_App1:2131361802
 xml:backup_rules:2131492864
 xml:data_extraction_rules:2131492865
 xml:file_paths:2131492866
 xml:network_security_config:2131492867
Unused resources are: 
 color:androidx_core_ripple_material_light:2130837504
 color:androidx_core_secondary_text_default_material_light:2130837505
 color:black:2130837506
 color:call_notification_answer_color:2130837507
 color:call_notification_decline_color:2130837508
 color:ktalk:2130837510
 color:notification_action_color_filter:2130837511
 color:notification_icon_bg_color:2130837512
 color:purple_200:2130837513
 color:purple_500:2130837514
 color:purple_700:2130837515
 color:teal_200:2130837516
 color:teal_700:2130837517
 color:vector_tint_color:2130837518
 color:vector_tint_theme_color:2130837519
 color:white:2130837520
 dimen:compat_button_inset_horizontal_material:2130903040
 dimen:compat_button_inset_vertical_material:2130903041
 dimen:compat_button_padding_horizontal_material:2130903042
 dimen:compat_button_padding_vertical_material:2130903043
 dimen:compat_control_corner_material:2130903044
 dimen:compat_notification_large_icon_max_height:2130903045
 dimen:compat_notification_large_icon_max_width:2130903046
 dimen:notification_action_icon_size:2130903047
 dimen:notification_action_text_size:2130903048
 dimen:notification_big_circle_margin:2130903049
 dimen:notification_content_margin_start:2130903050
 dimen:notification_large_icon_height:2130903051
 dimen:notification_large_icon_width:2130903052
 dimen:notification_main_column_padding_top:2130903053
 dimen:notification_media_narrow_margin:2130903054
 dimen:notification_right_icon_size:2130903055
 dimen:notification_right_side_padding_top:2130903056
 dimen:notification_small_icon_background_padding:2130903057
 dimen:notification_small_icon_size_as_large:2130903058
 dimen:notification_subtext_size:2130903059
 dimen:notification_top_pad:2130903060
 dimen:notification_top_pad_large_text:2130903061
 drawable:ic_call_answer:2130968577
 drawable:ic_call_answer_low:2130968578
 drawable:ic_call_answer_video:2130968579
 drawable:ic_call_answer_video_low:2130968580
 drawable:ic_call_decline:2130968581
 drawable:ic_call_decline_low:2130968582
 drawable:ic_launcher_background:2130968584
 drawable:notification_action_background:2130968586
 drawable:notification_bg:2130968587
 drawable:notification_bg_low:2130968588
 drawable:notification_bg_low_normal:2130968589
 drawable:notification_bg_low_pressed:2130968590
 drawable:notification_bg_normal:2130968591
 drawable:notification_bg_normal_pressed:2130968592
 drawable:notification_icon_background:2130968593
 drawable:notification_oversize_large_icon_bg:2130968594
 drawable:notification_template_icon_bg:2130968595
 drawable:notification_template_icon_low_bg:2130968596
 drawable:notification_tile_bg:2130968597
 drawable:notify_panel_notification_icon_bg:2130968598
 id:action_container:2131034145
 id:action_divider:2131034146
 id:action_image:2131034147
 id:action_text:2131034148
 id:actions:2131034149
 id:androidx_window_activity_scope:2131034151
 id:async:2131034152
 id:blocking:2131034153
 id:chronometer:2131034154
 id:coil3_request_manager:2131034155
 id:dialog_button:2131034158
 id:edit_text_id:2131034159
 id:forever:2131034160
 id:hide_ime_id:2131034162
 id:icon:2131034164
 id:icon_group:2131034165
 id:info:2131034166
 id:italic:2131034169
 id:line1:2131034170
 id:line3:2131034171
 id:locale:2131034172
 id:ltr:2131034173
 id:nav_controller_view_tag:2131034174
 id:normal:2131034175
 id:notification_background:2131034176
 id:notification_main_column:2131034177
 id:notification_main_column_container:2131034178
 id:right_icon:2131034181
 id:right_side:2131034182
 id:rtl:2131034183
 id:tag_on_receive_content_listener:2131034190
 id:tag_on_receive_content_mime_types:2131034191
 id:tag_system_bar_state_monitor:2131034194
 id:tag_transition_group:2131034195
 id:text:2131034199
 id:text2:2131034200
 id:time:2131034201
 id:title:2131034202
 id:webview_template_tag_key:2131034208
 integer:status_bar_notification_info_maxnum:2131099649
 layout:custom_dialog:2131165184
 layout:ime_base_split_test_activity:2131165185
 layout:ime_secondary_split_test_activity:2131165186
 layout:notification_action:2131165187
 layout:notification_action_tombstone:2131165188
 layout:notification_template_custom_big:2131165189
 layout:notification_template_icon_group:2131165190
 layout:notification_template_part_chronometer:2131165191
 layout:notification_template_part_time:2131165192
 mipmap:ic_launcher_foreground:2131230721
 mipmap:kztalk:2131230723
 string:autofill:2131296259
 string:call_notification_answer_action:2131296260
 string:call_notification_answer_video_action:2131296261
 string:call_notification_decline_action:2131296262
 string:call_notification_hang_up_action:2131296263
 string:call_notification_incoming_text:2131296264
 string:call_notification_ongoing_text:2131296265
 string:call_notification_screening_text:2131296266
 string:close_drawer:2131296268
 string:dropdown_menu:2131296275
 string:m3c_date_input_headline:2131296283
 string:m3c_date_input_headline_description:2131296284
 string:m3c_date_input_invalid_for_pattern:2131296285
 string:m3c_date_input_invalid_not_allowed:2131296286
 string:m3c_date_input_invalid_year_range:2131296287
 string:m3c_date_input_label:2131296288
 string:m3c_date_input_no_input_description:2131296289
 string:m3c_date_input_title:2131296290
 string:m3c_date_picker_headline:2131296291
 string:m3c_date_picker_headline_description:2131296292
 string:m3c_date_picker_navigate_to_year_description:2131296293
 string:m3c_date_picker_no_selection_description:2131296294
 string:m3c_date_picker_scroll_to_earlier_years:2131296295
 string:m3c_date_picker_scroll_to_later_years:2131296296
 string:m3c_date_picker_switch_to_calendar_mode:2131296297
 string:m3c_date_picker_switch_to_day_selection:2131296298
 string:m3c_date_picker_switch_to_input_mode:2131296299
 string:m3c_date_picker_switch_to_next_month:2131296300
 string:m3c_date_picker_switch_to_previous_month:2131296301
 string:m3c_date_picker_switch_to_year_selection:2131296302
 string:m3c_date_picker_title:2131296303
 string:m3c_date_picker_today_description:2131296304
 string:m3c_date_picker_year_picker_pane_title:2131296305
 string:m3c_date_range_input_invalid_range_input:2131296306
 string:m3c_date_range_input_title:2131296307
 string:m3c_date_range_picker_day_in_range:2131296308
 string:m3c_date_range_picker_end_headline:2131296309
 string:m3c_date_range_picker_scroll_to_next_month:2131296310
 string:m3c_date_range_picker_scroll_to_previous_month:2131296311
 string:m3c_date_range_picker_start_headline:2131296312
 string:m3c_date_range_picker_title:2131296313
 string:m3c_search_bar_search:2131296318
 string:m3c_snackbar_dismiss:2131296319
 string:m3c_suggestions_available:2131296320
 string:m3c_time_picker_am:2131296321
 string:m3c_time_picker_hour:2131296322
 string:m3c_time_picker_hour_24h_suffix:2131296323
 string:m3c_time_picker_hour_selection:2131296324
 string:m3c_time_picker_hour_suffix:2131296325
 string:m3c_time_picker_hour_text_field:2131296326
 string:m3c_time_picker_minute:2131296327
 string:m3c_time_picker_minute_selection:2131296328
 string:m3c_time_picker_minute_suffix:2131296329
 string:m3c_time_picker_minute_text_field:2131296330
 string:m3c_time_picker_period_toggle_description:2131296331
 string:m3c_time_picker_pm:2131296332
 string:m3c_tooltip_long_press_label:2131296333
 string:m3c_tooltip_pane_description:2131296334
 string:no_app_found_for_link:2131296336
 string:no_permission_open_link:2131296337
 string:range_end:2131296339
 string:range_start:2131296340
 string:snackbar_pane_title:2131296342
 string:status_bar_notification_info_overflow:2131296346
 string:tooltip_description:2131296350
 string:tooltip_label:2131296351
 style:TextAppearance_Compat_Notification:2131361797
 style:TextAppearance_Compat_Notification_Info:2131361798
 style:TextAppearance_Compat_Notification_Line2:2131361799
 style:TextAppearance_Compat_Notification_Time:2131361800
 style:TextAppearance_Compat_Notification_Title:2131361801
 style:Widget_Compat_NotificationActionContainer:2131361803
 style:Widget_Compat_NotificationActionText:2131361804
