:com.example.everytalk.data.DataClass.ApiConfig.$serializer8com.example.everytalk.data.DataClass.ApiContentPart.TextDcom.example.everytalk.data.DataClass.ApiContentPart.Text.$serializer;com.example.everytalk.data.DataClass.ApiContentPart.FileUriGcom.example.everytalk.data.DataClass.ApiContentPart.FileUri.$serializer>com.example.everytalk.data.DataClass.ApiContentPart.InlineDataJcom.example.everytalk.data.DataClass.ApiContentPart.InlineData.$serializer7com.example.everytalk.data.DataClass.AbstractApiMessage9com.example.everytalk.data.DataClass.SimpleTextApiMessageEcom.example.everytalk.data.DataClass.SimpleTextApiMessage.$<EMAIL>.$serializer<com.example.everytalk.data.DataClass.ChatRequest.$serializer5com.example.everytalk.data.DataClass.ContentPart.Html5com.example.everytalk.data.DataClass.ContentPart.Code6com.example.everytalk.data.DataClass.ContentPart.AudioAcom.example.everytalk.data.DataClass.GeminiApiRequest.$serializer8com.example.everytalk.data.DataClass.Content.$serializer.com.example.everytalk.data.DataClass.Part.Text:com.example.everytalk.data.DataClass.Part.Text.$<EMAIL>.$serializer1com.example.everytalk.data.DataClass.Part.FileUri=com.example.everytalk.data.DataClass.Part.FileUri.$serializer>com.example.everytalk.data.DataClass.SafetySetting.$serializerBcom.example.everytalk.data.DataClass.GeminiApiResponse.$serializer:com.example.everytalk.data.DataClass.Candidate.$serializer=com.example.everytalk.data.DataClass.SafetyRating.$serializer?com.example.everytalk.data.DataClass.PromptFeedback.$serializer?com.example.everytalk.data.DataClass.ThinkingConfig.$serializerAcom.example.everytalk.data.DataClass.GenerationConfig.$serializer>com.example.everytalk.data.DataClass.GithubRelease.$serializer+com.example.everytalk.data.DataClass.Sender,com.example.everytalk.data.DataClass.Message8com.example.everytalk.data.DataClass.Message.$<EMAIL>.$serializer0com.example.everytalk.data.network.AnySerializerRcom.example.everytalk.data.network.ApiClient.FileUploadInitialResponse.$serializerEcom.example.everytalk.data.network.ApiClient.FileMetadata.$serializer7com.example.everytalk.data.network.ApiMessageSerializer6com.example.everytalk.data.network.AppStreamEvent.TextBcom.example.everytalk.data.network.AppStreamEvent.Text.$serializer9com.example.everytalk.data.network.AppStreamEvent.ContentEcom.example.everytalk.data.network.AppStreamEvent.Content.$serializer;com.example.everytalk.data.network.AppStreamEvent.ReasoningGcom.example.everytalk.data.network.AppStreamEvent.Reasoning.$serializer;com.example.everytalk.data.network.AppStreamEvent.StreamEndGcom.example.everytalk.data.network.AppStreamEvent.StreamEnd.$serializerAcom.example.everytalk.data.network.AppStreamEvent.WebSearchStatusMcom.example.everytalk.data.network.AppStreamEvent.WebSearchStatus.$serializerBcom.example.everytalk.data.network.AppStreamEvent.WebSearchResultsNcom.example.everytalk.data.network.AppStreamEvent.WebSearchResults.$serializer:com.example.everytalk.data.network.AppStreamEvent.ToolCallFcom.example.everytalk.data.network.AppStreamEvent.ToolCall.$serializer7com.example.everytalk.data.network.AppStreamEvent.ErrorCcom.example.everytalk.data.network.AppStreamEvent.Error.$serializer8com.example.everytalk.data.network.AppStreamEvent.FinishDcom.example.everytalk.data.network.AppStreamEvent.Finish.$serializer=com.example.everytalk.data.network.OpenAiToolCall.$serializerAcom.example.everytalk.data.network.OpenAiFunctionCall.$serializer.com.example.everytalk.models.ImageSourceOption,com.example.everytalk.models.MoreOptionsType;com.example.everytalk.models.SelectedMediaItem.ImageFromUriGcom.example.everytalk.models.SelectedMediaItem.ImageFromUri.$serializer>com.example.everytalk.models.SelectedMediaItem.ImageFromBitmapJcom.example.everytalk.models.SelectedMediaItem.ImageFromBitmap.$serializer:com.example.everytalk.models.SelectedMediaItem.GenericFileFcom.example.everytalk.models.SelectedMediaItem.GenericFile.$<EMAIL>.$serializerPcom.example.everytalk.statecontroller.ApiHandler.BackendErrorContent.$serializer.com.example.everytalk.statecontroller.LRUCache2com.example.everytalk.statecontroller.AppViewModelOcom.example.everytalk.statecontroller.AppViewModel.ExportedSettings.$serializer9com.example.everytalk.statecontroller.AppViewModelFactory2com.example.everytalk.statecontroller.MainActivityCcom.example.everytalk.ui.screens.BubbleMain.Main.TextSegment.NormalFcom.example.everytalk.ui.screens.BubbleMain.Main.TextSegment.CodeBlockCcom.example.everytalk.ui.screens.BubbleMain.Main.TextSegment.HeaderEcom.example.everytalk.ui.screens.BubbleMain.Main.TextSegment.ListItem;com.example.everytalk.ui.screens.MainScreen.AiMessageOptionIcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.UserMessageLcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.AiMessageBlockPcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.AiMessageReasoningMcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.AiMessageFooterJcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.ErrorMessageNcom.example.everytalk.ui.screens.MainScreen.chat.ChatListItem.LoadingIndicatorIcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleState.IdleNcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleState.Animating+com.example.everytalk.util.BitmapSerializer/com.example.everytalk.util.MarkdownBlock.Header2com.example.everytalk.util.MarkdownBlock.CodeBlock.com.example.everytalk.util.MarkdownBlock.Image.com.example.everytalk.util.MarkdownBlock.Table2com.example.everytalk.util.MarkdownBlock.Paragraph3com.example.everytalk.util.MarkdownBlock.Blockquote6com.example.everytalk.util.MarkdownBlock.UnorderedList4com.example.everytalk.util.MarkdownBlock.OrderedList7com.example.everytalk.util.MarkdownBlock.HorizontalRule2com.example.everytalk.util.MarkdownBlock.MathBlock'com.example.everytalk.util.HeaderParser*com.example.everytalk.util.CodeBlockParser.com.example.everytalk.util.UnorderedListParser,com.example.everytalk.util.OrderedListParser&com.example.everytalk.util.ImageParser/com.example.everytalk.util.HorizontalRuleParser+com.example.everytalk.util.BlockquoteParser&com.example.everytalk.util.TableParser*com.example.everytalk.util.ParagraphParser*com.example.everytalk.util.MathBlockParser2com.example.everytalk.util.InlineElement.PlainText-com.example.everytalk.util.InlineElement.Bold/com.example.everytalk.util.InlineElement.Italic3com.example.everytalk.util.InlineElement.BoldItalic-com.example.everytalk.util.InlineElement.Code-com.example.everytalk.util.InlineElement.Link1com.example.everytalk.util.InlineElement.AutoLink-com.example.everytalk.util.InlineElement.Math>com.example.everytalk.util.IncrementalMarkdownParser.TokenType/com.example.everytalk.util.RenderMode.Streaming.com.example.everytalk.util.RenderMode.Complete><EMAIL><<EMAIL>(com.example.everytalk.util.UriSerializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               