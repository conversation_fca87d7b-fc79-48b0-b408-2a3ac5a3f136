<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.11.1" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="17"
            column="36"
            startOffset="925"
            endLine="17"
            endColumn="76"
            endOffset="965"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="27-∞" requiresApi="33-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="25"
            column="9"
            startOffset="1269"
            endLine="25"
            endColumn="51"
            endOffset="1311"/>
        <map>
            <entry
                name="message"
                string="Attribute `enableOnBackInvokedCallback` is only used in API level 33 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="31-∞"/>
            <api-levels id="requiresApi"
                value="33-∞"/>
        </map>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenWidthDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/MainScreen/AppDrawerContent.kt"
            line="80"
            column="23"
            startOffset="3561"
            endLine="80"
            endColumn="50"
            endOffset="3588"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenWidthDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/MainScreen/ChatScreen.kt"
            line="185"
            column="23"
            startOffset="7879"
            endLine="185"
            endColumn="50"
            endOffset="7906"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenHeightDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/MainScreen/ChatScreen.kt"
            line="581"
            column="33"
            startOffset="23411"
            endLine="581"
            endColumn="74"
            endOffset="23452"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenWidthDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/statecontroller/MainActivity.kt"
            line="138"
            column="45"
            startOffset="6124"
            endLine="138"
            endColumn="72"
            endOffset="6151"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenHeightDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/MainScreen/chat/ModelSelectionBottomSheet.kt"
            line="226"
            column="25"
            startOffset="9076"
            endLine="226"
            endColumn="53"
            endOffset="9104"/>
    </incident>

    <incident
        id="ConfigurationScreenWidthHeight"
        severity="warning"
        message="Using Configuration.screenHeightDp instead of LocalWindowInfo.current.containerSize">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/everytalk/ui/screens/BubbleMain/Main/ThinkingUI.kt"
            line="224"
            column="37"
            startOffset="9373"
            endLine="224"
            endColumn="78"
            endOffset="9414"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1570"
                endOffset="1590"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="31"
            column="9"
            startOffset="1570"
            endLine="31"
            endColumn="29"
            endOffset="1590"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
