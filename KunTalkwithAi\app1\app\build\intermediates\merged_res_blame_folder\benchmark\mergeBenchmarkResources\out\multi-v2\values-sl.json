{"logs": [{"outputFile": "com.example.everytalk.app-mergeBenchmarkResources-51:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "195,292,394,492,596,699,801,8587", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "287,389,487,591,694,796,913,8683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1537,1666,1795,1913,2042,2152,2248,2361,2501,2627,2770,2855,2954,3047,3144,3261,3383,3487,3624,3758,3889,4073,4200,4323,4448,4570,4664,4762,4882,5006,5106,5215,5321,5464,5611,5720,5822,5906,6001,6097,6205,6293,6379,6482,6564,6647,6742,6842,6933,7030,7118,7222,7319,7421,7563,7645,7751", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "1661,1790,1908,2037,2147,2243,2356,2496,2622,2765,2850,2949,3042,3139,3256,3378,3482,3619,3753,3884,4068,4195,4318,4443,4565,4659,4757,4877,5001,5101,5210,5316,5459,5606,5715,5817,5901,5996,6092,6200,6288,6374,6477,6559,6642,6737,6837,6928,7025,7113,7217,7314,7416,7558,7640,7746,7845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,291,372,468,566,651,728,815,907,989,1071,1157,1229,1317,1394,1474,1552,1630,1700", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "286,367,463,561,646,723,810,902,984,1066,1152,1224,1312,1389,1469,1547,1625,1695,1816"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "918,1013,1094,1190,1288,1373,1450,7850,7942,8024,8106,8192,8264,8352,8429,8509,8688,8766,8836", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "1008,1089,1185,1283,1368,1445,1532,7937,8019,8101,8187,8259,8347,8424,8504,8582,8761,8831,8952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,90", "endOffsets": "140,230,321"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8957,9047", "endColumns": "89,89,90", "endOffsets": "190,9042,9133"}}]}]}