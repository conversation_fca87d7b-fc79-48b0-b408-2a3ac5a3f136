{"logs": [{"outputFile": "com.example.everytalk.app-mergeBenchmarkResources-51:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4496,4582,4668,4773,4859,4946,5049,5151,5246,5349,5435,5536,5634,5736,5863,5949,6049", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4491,4577,4663,4768,4854,4941,5044,5146,5241,5344,5430,5531,5629,5731,5858,5944,6044,6139"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1540,1654,1766,1876,1987,2084,2180,2293,2422,2543,2674,2759,2859,2949,3049,3167,3287,3392,3519,3644,3774,3922,4043,4157,4276,4388,4479,4578,4691,4816,4910,5026,5132,5259,5393,5503,5600,5680,5778,5874,5981,6067,6153,6258,6344,6431,6534,6636,6731,6834,6920,7021,7119,7221,7348,7434,7534", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "1649,1761,1871,1982,2079,2175,2288,2417,2538,2669,2754,2854,2944,3044,3162,3282,3387,3514,3639,3769,3917,4038,4152,4271,4383,4474,4573,4686,4811,4905,5021,5127,5254,5388,5498,5595,5675,5773,5869,5976,6062,6148,6253,6339,6426,6529,6631,6726,6829,6915,7016,7114,7216,7343,7429,7529,7624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,84", "endOffsets": "137,223,308"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8721,8807", "endColumns": "86,85,84", "endOffsets": "187,8802,8887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,283,360,469,567,656,745,835,921,1004,1084,1168,1242,1330,1411,1486,1561,1639,1705", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "278,355,464,562,651,740,830,916,999,1079,1163,1237,1325,1406,1481,1556,1634,1700,1821"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "898,988,1065,1174,1272,1361,1450,7629,7715,7798,7878,7962,8036,8124,8205,8280,8456,8534,8600", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "983,1060,1169,1267,1356,1445,1535,7710,7793,7873,7957,8031,8119,8200,8275,8350,8529,8595,8716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "192,288,391,490,588,689,787,8355", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "283,386,485,583,684,782,893,8451"}}]}]}