[{"merged": "com.example.everytalk.app-release-54:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.everytalk.app-main-55:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.everytalk.app-release-54:/layout-v21_notification_action.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout-v21/notification_action.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable-v21_notification_action_background.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-v21/notification_action_background.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable-mdpi-v4_notification_bg_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-mdpi-v4/notification_bg_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/xml_file_paths.xml.flat", "source": "com.example.everytalk.app-main-55:/xml/file_paths.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-release-54:/layout_ime_secondary_split_test_activity.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-release-54:/layout_custom_dialog.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout/custom_dialog.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-mdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/drawable-mdpi-v4_notification_bg_low_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-mdpi-v4/notification_bg_low_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xhdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xhdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable-xhdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.everytalk.app-release-54:/layout_notification_template_part_time.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout/notification_template_part_time.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxhdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxhdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notification_bg_normal_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notification_bg_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notification_bg_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_notification_icon_background.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/notification_icon_background.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notification_bg_low_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_answer_low.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_answer_low.xml"}, {"merged": "com.example.everytalk.app-release-54:/layout_ime_base_split_test_activity.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout/ime_base_split_test_activity.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-xhdpi-v4_notification_bg_low_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-xhdpi-v4/notification_bg_low_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/xml_data_extraction_rules.xml.flat", "source": "com.example.everytalk.app-main-55:/xml/data_extraction_rules.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.everytalk.app-release-54:/layout_notification_template_part_chronometer.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout/notification_template_part_chronometer.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.everytalk.app-main-55:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-mdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-hdpi_kztalk.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-hdpi/kztalk.webp"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.everytalk.app-main-55:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_answer.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_answer.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notification_oversize_large_icon_bg.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png"}, {"merged": "com.example.everytalk.app-release-54:/drawable-mdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-mdpi-v4/notification_bg_low_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/layout-v21_notification_action_tombstone.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.example.everytalk.app-release-54:/layout-v21_notification_template_icon_group.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout-v21/notification_template_icon_group.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png"}, {"merged": "com.example.everytalk.app-release-54:/layout-v21_notification_template_custom_big.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/layout-v21/notification_template_custom_big.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_decline.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_decline.xml"}, {"merged": "com.example.everytalk.app-release-54:/color_vector_tint_theme_color.xml.flat", "source": "com.example.everytalk.app-ui-release-42:/color/vector_tint_theme_color.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable_abc_vector_test.xml.flat", "source": "com.example.everytalk.app-appcompat-resources-1.7.0-10:/drawable/abc_vector_test.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_notification_bg.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/notification_bg.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-release-54:/xml_backup_rules.xml.flat", "source": "com.example.everytalk.app-main-55:/xml/backup_rules.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-xhdpi-v4_notification_bg_low_pressed.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png"}, {"merged": "com.example.everytalk.app-release-54:/drawable-xhdpi-v4_notification_bg_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-xhdpi-v4/notification_bg_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/color_vector_tint_color.xml.flat", "source": "com.example.everytalk.app-ui-release-42:/color/vector_tint_color.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_notification_tile_bg.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/notification_tile_bg.xml"}, {"merged": "com.example.everytalk.app-release-54:/xml_network_security_config.xml.flat", "source": "com.example.everytalk.app-main-55:/xml/network_security_config.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_answer_video_low.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_answer_video_low.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_foreground_logo.webp.flat", "source": "com.example.everytalk.app-main-55:/drawable/ic_foreground_logo.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_answer_video.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_answer_video.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-hdpi-v4_notification_bg_low_normal.9.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-hdpi-v4/notification_bg_low_normal.9.png"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_notification_bg_low.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/notification_bg_low.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_launcher_background.xml.flat", "source": "com.example.everytalk.app-main-55:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.everytalk.app-release-54:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.everytalk.app-main-55:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.everytalk.app-release-54:/drawable_ic_call_decline_low.xml.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable/ic_call_decline_low.xml"}, {"merged": "com.example.everytalk.app-release-54:/drawable-xhdpi-v4_notify_panel_notification_icon_bg.png.flat", "source": "com.example.everytalk.app-core-1.16.0-41:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png"}]