# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.20"
  }
  digests {
    sha256: "\033\314t\350\316\204\342\302^\252\375\341\017\022H4\234\3160b\266\343ix\313\356\306\020\333\036\223\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.20"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.12.01"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-android"
    version: "1.7.6"
  }
  digests {
    sha256: "s\233EG\340(\244|9\035\200\3471\367\270-\256?\b<\251\022\261\323a\'E\313L(\202\227"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\003F3To\343\322cxC\301\355q\374\272J\3618\255\322v\217\370\314!f?\307\252\221\223\266"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.5.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.5.0"
  }
  digests {
    sha256: "p\263Y$\344\272\274\337\3727\320\345u\356\003\234V\242\331q#4&$\304\213`23pCA"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.5.0"
  }
  digests {
    sha256: "\223\233J\206\227d\016w\330>N\213LJ\235\030\370\023\001\230\305\226\021\224\257\215\331\333\235\307\303S"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\211\344\217hI\313\016\'\260\265.\241\031LX\251\230\375W\344\004\225\030\327\254\234K\227\017\242\200\346"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\b\002cDS`\204eYX\022\v\314:\250\375\201+\264N\v\201\233?\342\347\240#\364!\257\330"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.10.2"
  }
  digests {
    sha256: "\347\023\361\370t$A\025\240uq\006\\\377\240\362O^x0\016\227 \376\241m\343\257\035u\375A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.10.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.10.2"
  }
  digests {
    sha256: "\\\241u\263\215\3631\375d\025[5\315\214\256\022Q\372\236\343ip\2336\324.\n(\214\314\343\375"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.10.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-jdk8"
    version: "1.10.2"
  }
  digests {
    sha256: "\370a\371\372;0\210\346\367\305\233\311*\221\255\254\366\207\265\275\353\b\034\363`\214TT\367s\226%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-slf4j"
    version: "1.10.2"
  }
  digests {
    sha256: "\326]\356<\257\246\367\277\256\376\333I?\306\213\\c\253\347PsO\270\257\222\372\307\240\323\020\024W"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.12"
  }
  digests {
    sha256: "\247\225\002\270\253\337\275r(F\242v\221\"j@\210h-m5eO\233\200\342\251\314\254\367\355G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\264z\255\f\241R\022\376\225\301\\@H\264\360\211\316\005Joy\213\034\266\304W\366\272\373\023)\030"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\257\333PE\243K\275Y)\000\vy\200f\341N\350\242\321{\225s\346\321M\315\236\200$^\307\271"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "}\177\374\f\307l\225\201A<\250\251\256?ginI\354\250\v6C\313\255\222B\207\221\037N\200"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json"
    version: "1.7.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-json-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\261\351\023\204\231\355\215 7^\335\243\362\261\311_1\003\242X\357\366\257\236\334^\240q\000\362\262\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.1"
  }
  digests {
    sha256: "\265\031\3711}\355\036,\034)\223\003\214\006\222\343\r\243&\312\231\t}\2231\377-:Xa\244("
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.8.0"
  }
  digests {
    sha256: "o\352\374\240\360\233o\242\331\033\214B\026\3654\276\232Y\257\001\231\r\302\024;~\b)\250\341\345\216"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.8.0"
  }
  digests {
    sha256: "$\310\306\375\321\356-\210?,|\271|q\333\t\376\221\325\264\237\022\325\222-\261c:#\a\v\350"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.8.0"
  }
  digests {
    sha256: "E\372\335-\b\264Rp!e\023 \024\225\003\310\\\246\355@\260&G\r\261vQf\032\0330\364"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\224\210\210\323w\300]\000p\235\036\204\306\\\273\214\363\330\334Vz\006\271{\"\241\205\\O\347 W"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\036\"\236\t\276d\016:\204\251R\332\0016;t\352\333c\177\016\v\212a\325YG\310\023\277\271\016"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.8.0"
  }
  digests {
    sha256: "6\333\345\374\316\021KF\020V]i\301\036\246\032D\340\341\246\"\347#\234\\\314U\000Q\305\0271"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.8.0"
  }
  digests {
    sha256: "e\272\377q\307{\332b*! ,\233\274t\'V\217\346\377\256\310\2240\333\367N\363w\026\332T"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.8.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.8.0"
  }
  digests {
    sha256: "\340-U\306\336\336\3669&\317\302\250\206\223\345\314,V\235\000\0174\236C\256al\234\003\275\267\016"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.6"
  }
  digests {
    sha256: "OK\027m\t\031\021A\3440#\310[\271\365\247\336\217Q\236\375\331\f=\n}\311S\263\262\250\332"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.6"
  }
  digests {
    sha256: "H\321\202\005\0226\261\337\354\202\2333\277M?\320m\003\302\207mS\"\b\343\367I\001Y\326\\F"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.7.6"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended-android"
    version: "1.7.6"
  }
  digests {
    sha256: "\a\351~R\363]x\236\364u\331\306\207\345\312F\314T\371\240\324\257\216\035\312\222\254%\373!E\f"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.1"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.1"
  }
  digests {
    sha256: "u$s\324\352\'\037\202(X\303\023\354\r\205z\260RI\002\031\006\363\000[0\362\261\374\320\334\300"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-window-size-class"
    version: "1.3.1"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-window-size-class-android"
    version: "1.3.1"
  }
  digests {
    sha256: "o\235\337\347\001\224\343\3540\254v\a\214!P\366I\177\346\300\247\021\025\035e\324v\330\nDwy"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-core"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-core-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "T\032\207\b`\346\032\273\375\322\211R\313\'\r \021\377fu}\363\205\374\254e\353=\035\301\"\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-http"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-http-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\005\0033\341\270\263\3770p\2762\026\341\363\356x\372\235e\n\310\322\021\004\035\005\277H%\233M\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-utils"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-utils-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\325\b\350\2052\224\310\247.V\032\022\220\022}\356\257\222t.-\201\003jk\233s\310+1\355a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-io"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-io-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\234\233t\277\036\361\026\344\262m\\\224P\232\005w\274\241@\3700\250\f\363\365-$T\315\001\022\033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-events"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-events-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\367`\346\223\273ij\257l\254XD\\R6\205\215\020\315|\025\bJ\302\fzu\005\256t\221\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-websocket-serialization"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-websocket-serialization-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\362O\004Lxn\344\000\341$\000u\030\324\aa\314mc\2660\251[x\257\347\232\242u\377+:"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "L/4\"&\212\225\376\267w\fu\321\322~\t\320\t\335i|\233K\362o\3311s2\333P>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-websockets"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-websockets-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\3200{\016\377\3064\2262\231\303\265/Q?x\317\265\305f:\356\336\235\304$\270U\233S\353\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-android"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-android-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\221\001\3409\263\337\034\217i:|IU\n\333\031S\252R\313\324\253\031\215\312ym1\242\220\353\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-content-negotiation"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-content-negotiation-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\037J\351j\342\t+.K\274\354VZ\351\302\321\361\030\207H\223o\0018\303\304\274\023\335m\335]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization-kotlinx-json"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization-kotlinx-json-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\t\356J\252\021*\276mSw\030\332\347d\rk\277\374\277\021\211\274)\302\241V\206\365=-\343\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization-kotlinx"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-serialization-kotlinx-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "\365\3325\215#\237\374m\350B\001\305/\362\207=\235\245\377\354\324^\033{n\\,km\337\\\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-logging"
    version: "2.3.11"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.ktor"
    artifactId: "ktor-client-logging-jvm"
    version: "2.3.11"
  }
  digests {
    sha256: "On\210}\202m\t\254w4\367|\335\272%h\220\234r\277\311Z\327L(x\201\263\306?\336g"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.7"
  }
  digests {
    sha256: "\365\005\256\351\212\372$\372\253\243n\n\242\370\313\351\267\245\200\b\272*\003(j\vz\036h\356\'\177"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.7"
  }
  digests {
    sha256: "\255@\304\255\256\204\200v\376\364\033tt\231\241\005\017ERS\234\017B\267Ge\344:w\336\256\332"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.7"
  }
  digests {
    sha256: "\205\233\374\222\3316\373\257\207\027?W\265\271\030\305R\372\335\\Z\223\036\376\026\327\216\232\330e\266\267"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.7"
  }
  digests {
    sha256: "9\331\275;\370\201\205\272\327\212\256\320\035\210\223\363u\334\237\016i\236P\227\377dA\242\335\212\257)"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.7"
  }
  digests {
    sha256: "\v^\aR\207 :6\364j\373L\355\243\226Gh\242x\311\263gh\344\270\313\262\224\357\001\324\364"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-nop"
    version: "2.0.12"
  }
  digests {
    sha256: "\202\215b*RS\216\375*F\005R\330\3144Zz\031\251\375hc\220\316\330\004\0160F(\227\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.commonmark"
    artifactId: "commonmark"
    version: "0.24.0"
  }
  digests {
    sha256: "g\2238\340\267\374\025\300-\']Y\206T\260\032\024\230\223\274(\250y\222\351\001#\310\320j\362["
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.commonmark"
    artifactId: "commonmark-ext-gfm-tables"
    version: "0.24.0"
  }
  digests {
    sha256: "\265M\3032\3711\346\320|\'f\024L\b{\b\363i6w\343h\025\032g\002\vN\225\273K\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.commonmark"
    artifactId: "commonmark-ext-gfm-strikethrough"
    version: "0.24.0"
  }
  digests {
    sha256: "s\205\313c\177\004\334L\275\244\335\312\234/\315*\367\254SjP\344\310\322\307\177GH\273\024\277A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.commonmark"
    artifactId: "commonmark-ext-autolink"
    version: "0.24.0"
  }
  digests {
    sha256: "\001;\244\363\272HP\241\3365\223]\025\001X|Q\217vC1\322y\200]\2434s\347\237_3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.nibor.autolink"
    artifactId: "autolink"
    version: "0.11.0"
  }
  digests {
    sha256: "9\306X\211H\2531\271\212\261\376\244\246\253\2537$?8|\264\214\265\n\345\231A\016\377\267\0008"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jsoup"
    artifactId: "jsoup"
    version: "1.17.2"
  }
  digests {
    sha256: "\366\v3\263\216\235z\311>\252\246\212lp\367\006\273\231\003d\224\262\342\255\322\277\356\021\320\232\306\365"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-compose"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-compose-android"
    version: "3.2.0"
  }
  digests {
    sha256: "!4/\264\352\r\022\226F\360-\253`P\227\222\203\233\r\356\275\356t\224%``\352\006\b\256="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-android"
    version: "3.2.0"
  }
  digests {
    sha256: "\226\354\250Dc$\201\344\027\346\370/~\024s\026k^\030\253\022I%\340\247\241\367\311\020=\267<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-core"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-core-android"
    version: "3.2.0"
  }
  digests {
    sha256: "\033\224\2605 {\032\247E0\032\376\310\354\005\000\354u\210\b\377\216\340\241#\035,\370\275\365V\033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.4.1"
  }
  digests {
    sha256: "\027\341zk\206\273T\aM:~\317\246\225\253\222\016\257\364\317fz\203q\355\202\343wX\f\374\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.11.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.11.0"
  }
  digests {
    sha256: "-\"T\032\031/\220\032E\311RtV}iW\254\036\326\023\324\236A\333N^]\206\023\234\360\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-compose-core"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-compose-core-android"
    version: "3.2.0"
  }
  digests {
    sha256: "\304\367OV\222\'Y0(\306\t\232\020\221\374\034Jc%\360L\230\321\337\261\302\314N\270\020\237\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.37.3"
  }
  digests {
    sha256: "\004S\304\305N\303\357\023\256\030\2703\000\361\322\240x\261\312\222\323\243x\0247Qf0\343\270P\337"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.foundation"
    artifactId: "foundation"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.animation"
    artifactId: "animation"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.animation"
    artifactId: "animation-core"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.annotation-internal"
    artifactId: "annotation"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.collection-internal"
    artifactId: "collection"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.runtime"
    artifactId: "runtime"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.4"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-geometry"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-util"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-graphics"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-unit"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-text"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.8.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-network-okhttp"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-network-okhttp-jvm"
    version: "3.2.0"
  }
  digests {
    sha256: "}\'\376\227:8*\233\333\036\335\274_L3\354\302+\354na}\214\022o\t\344(s\210\265\t"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-network-core"
    version: "3.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt.coil3"
    artifactId: "coil-network-core-android"
    version: "3.2.0"
  }
  digests {
    sha256: "\254\364\360\226\310\021\n\b\323C\030\235\331\032{_+\340.\274B\327\257\277Nm\233D?F\210\364"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 95
  library_dep_index: 97
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 29
  library_dep_index: 85
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 11
  library_dep_index: 91
  library_dep_index: 7
  library_dep_index: 96
  library_dep_index: 98
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 16
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 82
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 74
  library_dep_index: 94
  library_dep_index: 90
  library_dep_index: 12
  library_dep_index: 92
  library_dep_index: 19
  library_dep_index: 30
  library_dep_index: 78
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 17
  library_dep_index: 75
  library_dep_index: 28
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 86
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 82
  library_dep_index: 76
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 62
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 91
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 76
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 0
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 15
  library_dep_index: 0
}
library_dependencies {
  library_index: 15
  library_dep_index: 13
  library_dep_index: 13
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 78
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 11
  library_dep_index: 0
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 0
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 0
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 24
  library_dep_index: 21
  library_dep_index: 23
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 0
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 0
}
library_dependencies {
  library_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 73
  library_dep_index: 13
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 88
  library_dep_index: 84
  library_dep_index: 60
  library_dep_index: 52
  library_dep_index: 59
  library_dep_index: 70
  library_dep_index: 69
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 45
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 89
  library_dep_index: 0
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
  library_dep_index: 50
  library_dep_index: 59
  library_dep_index: 69
  library_dep_index: 32
  library_dep_index: 72
}
library_dependencies {
  library_index: 32
  library_dep_index: 8
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 62
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 72
  library_dep_index: 31
  library_dep_index: 0
}
library_dependencies {
  library_index: 33
  library_dep_index: 8
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 34
  library_dep_index: 0
}
library_dependencies {
  library_index: 34
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 49
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 45
  library_dep_index: 33
  library_dep_index: 0
}
library_dependencies {
  library_index: 35
  library_dep_index: 8
  library_dep_index: 36
}
library_dependencies {
  library_index: 37
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 38
  library_dep_index: 8
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 45
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 41
  library_dep_index: 8
}
library_dependencies {
  library_index: 42
  library_dep_index: 8
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 46
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 47
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 56
  library_dep_index: 59
  library_dep_index: 46
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 8
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 8
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 43
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 64
  library_dep_index: 43
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 59
  library_dep_index: 46
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 8
  library_dep_index: 33
  library_dep_index: 58
  library_dep_index: 52
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 64
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 58
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 45
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 60
}
library_dependencies {
  library_index: 59
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 58
  library_dep_index: 47
  library_dep_index: 39
  library_dep_index: 50
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 56
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 8
  library_dep_index: 8
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 43
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 64
  library_dep_index: 69
  library_dep_index: 0
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 65
  library_dep_index: 64
  library_dep_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 64
}
library_dependencies {
  library_index: 69
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 62
  library_dep_index: 0
}
library_dependencies {
  library_index: 70
  library_dep_index: 8
  library_dep_index: 35
  library_dep_index: 48
  library_dep_index: 36
}
library_dependencies {
  library_index: 71
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 72
  library_dep_index: 31
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 43
  library_dep_index: 39
  library_dep_index: 52
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 32
}
library_dependencies {
  library_index: 73
  library_dep_index: 34
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 76
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 80
  library_dep_index: 0
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 87
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 81
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 18
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 78
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 84
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 85
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 84
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 8
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 74
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 80
  library_dep_index: 76
}
library_dependencies {
  library_index: 87
  library_dep_index: 34
  library_dep_index: 0
}
library_dependencies {
  library_index: 88
  library_dep_index: 33
  library_dep_index: 0
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 91
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 82
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 84
  library_dep_index: 0
  library_dep_index: 91
  library_dep_index: 0
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 89
  library_dep_index: 0
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 89
  library_dep_index: 18
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 95
  library_dep_index: 2
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 72
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 95
  library_dep_index: 93
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 82
  library_dep_index: 76
  library_dep_index: 46
  library_dep_index: 2
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 10
  library_dep_index: 18
  library_dep_index: 29
  library_dep_index: 80
  library_dep_index: 76
  library_dep_index: 103
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 99
}
library_dependencies {
  library_index: 103
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 34
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 112
  library_dep_index: 114
  library_dep_index: 2
  library_dep_index: 25
}
library_dependencies {
  library_index: 106
  library_dep_index: 107
}
library_dependencies {
  library_index: 107
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 108
  library_dep_index: 2
}
library_dependencies {
  library_index: 108
  library_dep_index: 109
}
library_dependencies {
  library_index: 109
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 110
  library_dep_index: 2
}
library_dependencies {
  library_index: 110
  library_dep_index: 111
}
library_dependencies {
  library_index: 111
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 2
}
library_dependencies {
  library_index: 112
  library_dep_index: 113
}
library_dependencies {
  library_index: 113
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 108
  library_dep_index: 2
}
library_dependencies {
  library_index: 114
  library_dep_index: 115
}
library_dependencies {
  library_index: 115
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 116
  library_dep_index: 2
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 118
  library_dep_index: 2
}
library_dependencies {
  library_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 119
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 2
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 104
  library_dep_index: 21
  library_dep_index: 2
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 104
  library_dep_index: 116
  library_dep_index: 2
}
library_dependencies {
  library_index: 124
  library_dep_index: 125
}
library_dependencies {
  library_index: 125
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 126
  library_dep_index: 67
  library_dep_index: 2
}
library_dependencies {
  library_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 127
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 106
  library_dep_index: 116
  library_dep_index: 64
  library_dep_index: 2
}
library_dependencies {
  library_index: 128
  library_dep_index: 129
}
library_dependencies {
  library_index: 129
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 104
  library_dep_index: 2
}
library_dependencies {
  library_index: 130
  library_dep_index: 72
  library_dep_index: 11
  library_dep_index: 91
  library_dep_index: 18
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 54
  library_dep_index: 131
  library_dep_index: 0
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 133
}
library_dependencies {
  library_index: 131
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 132
  library_dep_index: 130
  library_dep_index: 134
  library_dep_index: 133
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
  library_dep_index: 133
  library_dep_index: 130
  library_dep_index: 134
  library_dep_index: 131
}
library_dependencies {
  library_index: 133
  library_dep_index: 8
  library_dep_index: 15
  library_dep_index: 33
  library_dep_index: 43
  library_dep_index: 50
  library_dep_index: 59
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 69
  library_dep_index: 0
  library_dep_index: 132
  library_dep_index: 130
  library_dep_index: 134
  library_dep_index: 131
}
library_dependencies {
  library_index: 134
  library_dep_index: 31
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 50
  library_dep_index: 59
  library_dep_index: 133
  library_dep_index: 0
  library_dep_index: 133
  library_dep_index: 132
  library_dep_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 135
  library_dep_index: 26
}
library_dependencies {
  library_index: 137
  library_dep_index: 136
}
library_dependencies {
  library_index: 138
  library_dep_index: 136
}
library_dependencies {
  library_index: 139
  library_dep_index: 136
  library_dep_index: 140
}
library_dependencies {
  library_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 143
  library_dep_index: 144
  library_dep_index: 154
  library_dep_index: 0
}
library_dependencies {
  library_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 145
  library_dep_index: 146
  library_dep_index: 0
}
library_dependencies {
  library_index: 146
  library_dep_index: 147
}
library_dependencies {
  library_index: 147
  library_dep_index: 39
  library_dep_index: 8
  library_dep_index: 148
  library_dep_index: 33
  library_dep_index: 151
  library_dep_index: 70
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 152
  library_dep_index: 0
}
library_dependencies {
  library_index: 148
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 34
  library_dep_index: 149
  library_dep_index: 150
}
library_dependencies {
  library_index: 149
  library_dep_index: 8
  library_dep_index: 34
  library_dep_index: 13
}
library_dependencies {
  library_index: 150
  library_dep_index: 149
  library_dep_index: 38
  library_dep_index: 13
}
library_dependencies {
  library_index: 151
  library_dep_index: 8
  library_dep_index: 45
}
library_dependencies {
  library_index: 152
  library_dep_index: 153
}
library_dependencies {
  library_index: 153
  library_dep_index: 0
}
library_dependencies {
  library_index: 154
  library_dep_index: 155
}
library_dependencies {
  library_index: 155
  library_dep_index: 156
  library_dep_index: 146
  library_dep_index: 157
  library_dep_index: 0
}
library_dependencies {
  library_index: 156
  library_dep_index: 29
  library_dep_index: 20
  library_dep_index: 0
}
library_dependencies {
  library_index: 157
  library_dep_index: 89
  library_dep_index: 158
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 174
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 173
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 158
  library_dep_index: 11
  library_dep_index: 159
  library_dep_index: 161
  library_dep_index: 174
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 169
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 159
  library_dep_index: 16
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 172
  library_dep_index: 170
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 160
  library_dep_index: 8
}
library_dependencies {
  library_index: 161
  library_dep_index: 13
  library_dep_index: 0
}
library_dependencies {
  library_index: 162
  library_dep_index: 18
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 163
  library_dep_index: 29
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 166
  library_dep_index: 167
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 171
  library_dep_index: 173
  library_dep_index: 172
  library_dep_index: 170
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 164
  library_dep_index: 43
  library_dep_index: 160
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 165
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 164
  library_dep_index: 160
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 166
  library_dep_index: 60
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 0
}
library_dependencies {
  library_index: 167
  library_dep_index: 52
  library_dep_index: 160
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 168
  library_dep_index: 27
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 0
}
library_dependencies {
  library_index: 169
  library_dep_index: 74
  library_dep_index: 162
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 170
  library_dep_index: 76
  library_dep_index: 161
  library_dep_index: 0
}
library_dependencies {
  library_index: 171
  library_dep_index: 78
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 169
  library_dep_index: 172
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 172
  library_dep_index: 80
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 169
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 173
  library_dep_index: 82
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 171
  library_dep_index: 172
  library_dep_index: 170
  library_dep_index: 0
  library_dep_index: 21
}
library_dependencies {
  library_index: 174
  library_dep_index: 91
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 170
  library_dep_index: 0
}
library_dependencies {
  library_index: 175
  library_dep_index: 176
}
library_dependencies {
  library_index: 176
  library_dep_index: 146
  library_dep_index: 177
  library_dep_index: 179
  library_dep_index: 0
}
library_dependencies {
  library_index: 177
  library_dep_index: 178
}
library_dependencies {
  library_index: 178
  library_dep_index: 33
  library_dep_index: 146
  library_dep_index: 0
}
library_dependencies {
  library_index: 179
  library_dep_index: 152
  library_dep_index: 4
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 29
  dependency_index: 85
  dependency_index: 99
  dependency_index: 6
  dependency_index: 101
  dependency_index: 95
  dependency_index: 97
  dependency_index: 54
  dependency_index: 72
  dependency_index: 33
  dependency_index: 50
  dependency_index: 47
  dependency_index: 67
  dependency_index: 21
  dependency_index: 20
  dependency_index: 104
  dependency_index: 120
  dependency_index: 122
  dependency_index: 124
  dependency_index: 128
  dependency_index: 130
  dependency_index: 70
  dependency_index: 135
  dependency_index: 136
  dependency_index: 137
  dependency_index: 138
  dependency_index: 139
  dependency_index: 141
  dependency_index: 142
  dependency_index: 175
  dependency_index: 180
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/public"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/google"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://maven.pkg.jetbrains.space/public/p/compose/dev"
  }
}
