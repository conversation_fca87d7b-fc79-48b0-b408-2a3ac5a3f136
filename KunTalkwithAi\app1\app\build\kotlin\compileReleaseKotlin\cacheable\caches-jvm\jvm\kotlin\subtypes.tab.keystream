2kotlinx.serialization.internal.GeneratedSerializer3com.example.everytalk.data.DataClass.ApiContentPart-com.example.everytalk.data.DataClass.IMessage7com.example.everytalk.data.DataClass.AbstractApiMessage0com.example.everytalk.data.DataClass.ContentPart)com.example.everytalk.data.DataClass.Partkotlin.Enum!kotlinx.serialization.KSerializer;kotlinx.serialization.json.JsonContentPolymorphicSerializer1com.example.everytalk.data.network.AppStreamEvent.com.example.everytalk.models.SelectedMediaItemjava.util.LinkedHashMap#androidx.lifecycle.AndroidViewModel,androidx.lifecycle.ViewModelProvider.Factory#androidx.activity.ComponentActivity<com.example.everytalk.ui.screens.BubbleMain.Main.TextSegment=com.example.everytalk.ui.screens.MainScreen.chat.ChatListItemDcom.example.everytalk.ui.screens.MainScreen.drawer.CustomRippleState(com.example.everytalk.util.MarkdownBlock&com.example.everytalk.util.BlockParser(com.example.everytalk.util.InlineElement%com.example.everytalk.util.RenderMode/com.example.everytalk.util.ProcessedEventResult                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 