{"logs": [{"outputFile": "com.example.everytalk.app-mergeReleaseResources-51:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5d97d34d84112940f02348422c3753d\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,293,380,477,578,664,741,832,924,1009,1089,1174,1247,1337,1414,1493,1570,1649,1719", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "288,375,472,573,659,736,827,919,1004,1084,1169,1242,1332,1409,1488,1565,1644,1714,1832"}, "to": {"startLines": "10,11,12,13,14,15,16,74,75,76,77,78,79,80,81,82,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "921,1018,1105,1202,1303,1389,1466,7838,7930,8015,8095,8180,8253,8343,8420,8499,8677,8756,8826", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "1013,1100,1197,1298,1384,1461,1552,7925,8010,8090,8175,8248,8338,8415,8494,8571,8751,8821,8939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\39e186d1df9a6e64a80555ea95166efd\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4685,4774,4860,4967,5047,5130,5227,5330,5423,5521,5608,5716,5813,5915,6048,6128,6237", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4680,4769,4855,4962,5042,5125,5222,5325,5418,5516,5603,5711,5808,5910,6043,6123,6232,6331"}, "to": {"startLines": "17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1557,1674,1791,1919,2036,2135,2229,2340,2476,2596,2738,2823,2923,3018,3116,3232,3357,3462,3603,3743,3876,4056,4181,4301,4426,4548,4644,4742,4860,4990,5090,5192,5301,5443,5592,5701,5804,5881,5980,6078,6187,6276,6362,6469,6549,6632,6729,6832,6925,7023,7110,7218,7315,7417,7550,7630,7739", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "1669,1786,1914,2031,2130,2224,2335,2471,2591,2733,2818,2918,3013,3111,3227,3352,3457,3598,3738,3871,4051,4176,4296,4421,4543,4639,4737,4855,4985,5085,5187,5296,5438,5587,5696,5799,5876,5975,6073,6182,6271,6357,6464,6544,6627,6724,6827,6920,7018,7105,7213,7310,7412,7545,7625,7734,7833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e5127fbf4aa18facdfc5d332b39b8229\\transformed\\core-1.16.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "3,4,5,6,7,8,9,83", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "195,293,395,492,596,700,805,8576", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "288,390,487,591,695,800,916,8672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\78e087dd1cdd09d6432d260deb7bf0a8\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "2,87,88", "startColumns": "4,4,4", "startOffsets": "105,8944,9034", "endColumns": "89,89,91", "endOffsets": "190,9029,9121"}}]}]}