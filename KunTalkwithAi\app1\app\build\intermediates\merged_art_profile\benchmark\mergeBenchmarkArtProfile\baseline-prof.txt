# Baseline Profiles for navigation-common

HSPLandroidx/navigation/NavAction;-><init>(ILandroidx/navigation/NavOptions;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavAction;-><init>(ILandroidx/navigation/NavOptions;Landroid/os/Bundle;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavAction;->getDefaultArguments()Landroid/os/Bundle;
HSPLandroidx/navigation/NavAction;->getDestinationId()I
HSPLandroidx/navigation/NavAction;->getNavOptions()Landroidx/navigation/NavOptions;
HSPLandroidx/navigation/NavAction;->setNavOptions(Landroidx/navigation/NavOptions;)V
HSPLandroidx/navigation/NavArgument$Builder;-><init>()V
HSPLandroidx/navigation/NavArgument$Builder;->build()Landroidx/navigation/NavArgument;
HSPLandroidx/navigation/NavArgument$Builder;->setIsNullable(Z)Landroidx/navigation/NavArgument$Builder;
HSPLandroidx/navigation/NavArgument$Builder;->setType(Landroidx/navigation/NavType;)Landroidx/navigation/NavArgument$Builder;
HSPLandroidx/navigation/NavArgument;-><init>(Landroidx/navigation/NavType;ZLjava/lang/Object;Z)V
HSPLandroidx/navigation/NavArgument;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavArgument;->hashCode()I
HSPLandroidx/navigation/NavBackStackEntry$Companion;-><init>()V
HSPLandroidx/navigation/NavBackStackEntry$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavBackStackEntry$Companion;->create$default(Landroidx/navigation/NavBackStackEntry$Companion;Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;ILjava/lang/Object;)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavBackStackEntry$Companion;->create(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavBackStackEntry$defaultFactory$2;-><init>(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavBackStackEntry$savedStateHandle$2;-><init>(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavBackStackEntry;-><clinit>()V
HSPLandroidx/navigation/NavBackStackEntry;-><init>(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavBackStackEntry;-><init>(Landroid/content/Context;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/lifecycle/LifecycleOwner;Landroidx/navigation/NavViewModelStoreProvider;Ljava/lang/String;Landroid/os/Bundle;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavBackStackEntry;->getArguments()Landroid/os/Bundle;
HSPLandroidx/navigation/NavBackStackEntry;->getDestination()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavBackStackEntry;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/navigation/NavBackStackEntry;->getMaxLifecycle()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/navigation/NavBackStackEntry;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/navigation/NavBackStackEntry;->handleLifecycleEvent(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/navigation/NavBackStackEntry;->hashCode()I
HSPLandroidx/navigation/NavBackStackEntry;->setMaxLifecycle(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/navigation/NavBackStackEntry;->updateState()V
HSPLandroidx/navigation/NavDeepLinkRequest;-><init>(Landroid/content/Intent;)V
HSPLandroidx/navigation/NavDeepLinkRequest;-><init>(Landroid/net/Uri;Ljava/lang/String;Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination$Companion;-><init>()V
HSPLandroidx/navigation/NavDestination$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavDestination$Companion;->getDisplayName(Landroid/content/Context;I)Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;-><clinit>()V
HSPLandroidx/navigation/NavDestination;-><init>(Landroidx/navigation/Navigator;)V
HSPLandroidx/navigation/NavDestination;-><init>(Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination;->addArgument(Ljava/lang/String;Landroidx/navigation/NavArgument;)V
HSPLandroidx/navigation/NavDestination;->addInDefaultArgs(Landroid/os/Bundle;)Landroid/os/Bundle;
HSPLandroidx/navigation/NavDestination;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavDestination;->getArguments()Ljava/util/Map;
HSPLandroidx/navigation/NavDestination;->getId()I
HSPLandroidx/navigation/NavDestination;->getNavigatorName()Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;->getParent()Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavDestination;->getRoute()Ljava/lang/String;
HSPLandroidx/navigation/NavDestination;->hashCode()I
HSPLandroidx/navigation/NavDestination;->matchDeepLink(Landroidx/navigation/NavDeepLinkRequest;)Landroidx/navigation/NavDestination$DeepLinkMatch;
HSPLandroidx/navigation/NavDestination;->onInflate(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/navigation/NavDestination;->putAction(ILandroidx/navigation/NavAction;)V
HSPLandroidx/navigation/NavDestination;->setId(I)V
HSPLandroidx/navigation/NavDestination;->setLabel(Ljava/lang/CharSequence;)V
HSPLandroidx/navigation/NavDestination;->setParent(Landroidx/navigation/NavGraph;)V
HSPLandroidx/navigation/NavDestination;->setRoute(Ljava/lang/String;)V
HSPLandroidx/navigation/NavDestination;->supportsActions()Z
HSPLandroidx/navigation/NavGraph$Companion;-><init>()V
HSPLandroidx/navigation/NavGraph$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavGraph$iterator$1;-><init>(Landroidx/navigation/NavGraph;)V
HSPLandroidx/navigation/NavGraph$iterator$1;->hasNext()Z
HSPLandroidx/navigation/NavGraph$iterator$1;->next()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraph$iterator$1;->next()Ljava/lang/Object;
HSPLandroidx/navigation/NavGraph;-><clinit>()V
HSPLandroidx/navigation/NavGraph;-><init>(Landroidx/navigation/Navigator;)V
HSPLandroidx/navigation/NavGraph;->addDestination(Landroidx/navigation/NavDestination;)V
HSPLandroidx/navigation/NavGraph;->equals(Ljava/lang/Object;)Z
HSPLandroidx/navigation/NavGraph;->findNode(IZ)Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraph;->getNodes()Landroidx/collection/SparseArrayCompat;
HSPLandroidx/navigation/NavGraph;->getStartDestinationId()I
HSPLandroidx/navigation/NavGraph;->getStartDestinationRoute()Ljava/lang/String;
HSPLandroidx/navigation/NavGraph;->hashCode()I
HSPLandroidx/navigation/NavGraph;->iterator()Ljava/util/Iterator;
HSPLandroidx/navigation/NavGraph;->matchDeepLink(Landroidx/navigation/NavDeepLinkRequest;)Landroidx/navigation/NavDestination$DeepLinkMatch;
HSPLandroidx/navigation/NavGraph;->onInflate(Landroid/content/Context;Landroid/util/AttributeSet;)V
HSPLandroidx/navigation/NavGraph;->setStartDestinationId(I)V
HSPLandroidx/navigation/NavGraphNavigator;-><init>(Landroidx/navigation/NavigatorProvider;)V
HSPLandroidx/navigation/NavGraphNavigator;->createDestination()Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavGraphNavigator;->createDestination()Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavGraphNavigator;->navigate(Landroidx/navigation/NavBackStackEntry;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;)V
HSPLandroidx/navigation/NavGraphNavigator;->navigate(Ljava/util/List;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;)V
HSPLandroidx/navigation/NavOptions$Builder;-><init>()V
HSPLandroidx/navigation/NavOptions$Builder;->build()Landroidx/navigation/NavOptions;
HSPLandroidx/navigation/NavOptions$Builder;->setEnterAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setExitAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setLaunchSingleTop(Z)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopEnterAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopExitAnim(I)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setPopUpTo(IZZ)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions$Builder;->setRestoreState(Z)Landroidx/navigation/NavOptions$Builder;
HSPLandroidx/navigation/NavOptions;-><init>(ZZIZZIIII)V
HSPLandroidx/navigation/NavOptions;->hashCode()I
HSPLandroidx/navigation/NavOptions;->isPopUpToInclusive()Z
HSPLandroidx/navigation/NavOptions;->shouldLaunchSingleTop()Z
HSPLandroidx/navigation/NavOptions;->shouldPopUpToSaveState()Z
HSPLandroidx/navigation/NavOptions;->shouldRestoreState()Z
HSPLandroidx/navigation/NavType$Companion$BoolArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$BoolArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$BoolType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$BoolType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$FloatArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$FloatType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$IntType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$IntType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$LongArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$LongArrayType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$LongType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$LongType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion$ReferenceType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringArrayType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringType$1;-><init>()V
HSPLandroidx/navigation/NavType$Companion$StringType$1;->getName()Ljava/lang/String;
HSPLandroidx/navigation/NavType$Companion;-><init>()V
HSPLandroidx/navigation/NavType$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavType$Companion;->fromArgType(Ljava/lang/String;Ljava/lang/String;)Landroidx/navigation/NavType;
HSPLandroidx/navigation/NavType;-><clinit>()V
HSPLandroidx/navigation/NavType;-><init>(Z)V
HSPLandroidx/navigation/NavType;->isNullableAllowed()Z
HSPLandroidx/navigation/Navigator;-><init>()V
HSPLandroidx/navigation/Navigator;->getState()Landroidx/navigation/NavigatorState;
HSPLandroidx/navigation/Navigator;->isAttached()Z
HSPLandroidx/navigation/Navigator;->onAttach(Landroidx/navigation/NavigatorState;)V
HSPLandroidx/navigation/NavigatorProvider$Companion;-><init>()V
HSPLandroidx/navigation/NavigatorProvider$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavigatorProvider$Companion;->getNameForNavigator$navigation_common_release(Ljava/lang/Class;)Ljava/lang/String;
HSPLandroidx/navigation/NavigatorProvider$Companion;->validateName$navigation_common_release(Ljava/lang/String;)Z
HSPLandroidx/navigation/NavigatorProvider;-><clinit>()V
HSPLandroidx/navigation/NavigatorProvider;-><init>()V
HSPLandroidx/navigation/NavigatorProvider;->access$getAnnotationNames$cp()Ljava/util/Map;
HSPLandroidx/navigation/NavigatorProvider;->addNavigator(Landroidx/navigation/Navigator;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->addNavigator(Ljava/lang/String;Landroidx/navigation/Navigator;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->getNavigator(Ljava/lang/String;)Landroidx/navigation/Navigator;
HSPLandroidx/navigation/NavigatorProvider;->getNavigators()Ljava/util/Map;
HSPLandroidx/navigation/NavigatorState;-><init>()V
HSPLandroidx/navigation/NavigatorState;->getBackStack()Lkotlinx/coroutines/flow/StateFlow;
HSPLandroidx/navigation/NavigatorState;->getTransitionsInProgress()Lkotlinx/coroutines/flow/StateFlow;
HSPLandroidx/navigation/NavigatorState;->push(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavigatorState;->setNavigating(Z)V
Landroidx/navigation/FloatingWindow;
Landroidx/navigation/NavAction;
Landroidx/navigation/NavArgument$Builder;
Landroidx/navigation/NavArgument;
Landroidx/navigation/NavBackStackEntry$Companion;
Landroidx/navigation/NavBackStackEntry$defaultFactory$2;
Landroidx/navigation/NavBackStackEntry$savedStateHandle$2;
Landroidx/navigation/NavBackStackEntry;
Landroidx/navigation/NavDeepLinkRequest;
Landroidx/navigation/NavDestination$Companion;
Landroidx/navigation/NavDestination$DeepLinkMatch;
Landroidx/navigation/NavDestination;
Landroidx/navigation/NavGraph$Companion;
Landroidx/navigation/NavGraph$iterator$1;
Landroidx/navigation/NavGraph;
Landroidx/navigation/NavGraphNavigator;
Landroidx/navigation/NavOptions$Builder;
Landroidx/navigation/NavOptions;
Landroidx/navigation/NavType$Companion$BoolArrayType$1;
Landroidx/navigation/NavType$Companion$BoolType$1;
Landroidx/navigation/NavType$Companion$FloatArrayType$1;
Landroidx/navigation/NavType$Companion$FloatType$1;
Landroidx/navigation/NavType$Companion$IntArrayType$1;
Landroidx/navigation/NavType$Companion$IntType$1;
Landroidx/navigation/NavType$Companion$LongArrayType$1;
Landroidx/navigation/NavType$Companion$LongType$1;
Landroidx/navigation/NavType$Companion$ReferenceType$1;
Landroidx/navigation/NavType$Companion$StringArrayType$1;
Landroidx/navigation/NavType$Companion$StringType$1;
Landroidx/navigation/NavType$Companion;
Landroidx/navigation/NavType;
Landroidx/navigation/NavViewModelStoreProvider;
Landroidx/navigation/Navigator$Extras;
Landroidx/navigation/Navigator$Name;
Landroidx/navigation/Navigator;
Landroidx/navigation/NavigatorProvider$Companion;
Landroidx/navigation/NavigatorProvider;
Landroidx/navigation/NavigatorState;
HSPLandroidx/navigation/common/R$styleable;-><clinit>()V
Landroidx/navigation/common/R$styleable;

# Baseline Profiles for navigation-runtime

HSPLandroidx/navigation/ActivityNavigator$Companion;-><init>()V
HSPLandroidx/navigation/ActivityNavigator$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/ActivityNavigator$hostActivity$1;-><clinit>()V
HSPLandroidx/navigation/ActivityNavigator$hostActivity$1;-><init>()V
HSPLandroidx/navigation/ActivityNavigator;-><clinit>()V
HSPLandroidx/navigation/ActivityNavigator;-><init>(Landroid/content/Context;)V
HSPLandroidx/navigation/NavController$Companion;-><init>()V
HSPLandroidx/navigation/NavController$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavController$NavControllerNavigatorState;-><init>(Landroidx/navigation/NavController;Landroidx/navigation/Navigator;)V
HSPLandroidx/navigation/NavController$NavControllerNavigatorState;->addInternal(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavController$NavControllerNavigatorState;->createBackStackEntry(Landroidx/navigation/NavDestination;Landroid/os/Bundle;)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavController$NavControllerNavigatorState;->push(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavController$activity$1;-><clinit>()V
HSPLandroidx/navigation/NavController$activity$1;-><init>()V
HSPLandroidx/navigation/NavController$lifecycleObserver$1;-><init>(Landroidx/navigation/NavController;)V
HSPLandroidx/navigation/NavController$lifecycleObserver$1;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/navigation/NavController$navInflater$2;-><init>(Landroidx/navigation/NavController;)V
HSPLandroidx/navigation/NavController$navInflater$2;->invoke()Landroidx/navigation/NavInflater;
HSPLandroidx/navigation/NavController$navInflater$2;->invoke()Ljava/lang/Object;
HSPLandroidx/navigation/NavController$navigate$4;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;Landroidx/navigation/NavController;Landroidx/navigation/NavDestination;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavController$navigate$4;->invoke(Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavController$navigate$4;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
HSPLandroidx/navigation/NavController$onBackPressedCallback$1;-><init>(Landroidx/navigation/NavController;)V
HSPLandroidx/navigation/NavController;-><clinit>()V
HSPLandroidx/navigation/NavController;-><init>(Landroid/content/Context;)V
HSPLandroidx/navigation/NavController;->access$getAddToBackStackHandler$p(Landroidx/navigation/NavController;)Lkotlin/jvm/functions/Function1;
HSPLandroidx/navigation/NavController;->access$getInflater$p(Landroidx/navigation/NavController;)Landroidx/navigation/NavInflater;
HSPLandroidx/navigation/NavController;->access$getLifecycleOwner$p(Landroidx/navigation/NavController;)Landroidx/lifecycle/LifecycleOwner;
HSPLandroidx/navigation/NavController;->access$getViewModel$p(Landroidx/navigation/NavController;)Landroidx/navigation/NavControllerViewModel;
HSPLandroidx/navigation/NavController;->access$get_graph$p(Landroidx/navigation/NavController;)Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavController;->access$get_navigatorProvider$p(Landroidx/navigation/NavController;)Landroidx/navigation/NavigatorProvider;
HSPLandroidx/navigation/NavController;->addEntryToBackStack$default(Landroidx/navigation/NavController;Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/navigation/NavBackStackEntry;Ljava/util/List;ILjava/lang/Object;)V
HSPLandroidx/navigation/NavController;->addEntryToBackStack(Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/navigation/NavBackStackEntry;Ljava/util/List;)V
HSPLandroidx/navigation/NavController;->dispatchOnDestinationChanged()Z
HSPLandroidx/navigation/NavController;->enableOnBackPressed(Z)V
HSPLandroidx/navigation/NavController;->findDestination(I)Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavController;->getBackQueue()Lkotlin/collections/ArrayDeque;
HSPLandroidx/navigation/NavController;->getBackStackEntry(I)Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavController;->getContext()Landroid/content/Context;
HSPLandroidx/navigation/NavController;->getCurrentBackStackEntry()Landroidx/navigation/NavBackStackEntry;
HSPLandroidx/navigation/NavController;->getDestinationCountOnBackStack()I
HSPLandroidx/navigation/NavController;->getNavInflater()Landroidx/navigation/NavInflater;
HSPLandroidx/navigation/NavController;->getNavigatorProvider()Landroidx/navigation/NavigatorProvider;
HSPLandroidx/navigation/NavController;->handleDeepLink(Landroid/content/Intent;)Z
HSPLandroidx/navigation/NavController;->linkChildToParent(Landroidx/navigation/NavBackStackEntry;Landroidx/navigation/NavBackStackEntry;)V
HSPLandroidx/navigation/NavController;->navigate(Landroidx/navigation/NavDestination;Landroid/os/Bundle;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;)V
HSPLandroidx/navigation/NavController;->navigateInternal(Landroidx/navigation/Navigator;Ljava/util/List;Landroidx/navigation/NavOptions;Landroidx/navigation/Navigator$Extras;Lkotlin/jvm/functions/Function1;)V
HSPLandroidx/navigation/NavController;->onGraphCreated(Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavController;->populateVisibleEntries$navigation_runtime_release()Ljava/util/List;
HSPLandroidx/navigation/NavController;->setGraph(I)V
HSPLandroidx/navigation/NavController;->setGraph(Landroidx/navigation/NavGraph;Landroid/os/Bundle;)V
HSPLandroidx/navigation/NavController;->setLifecycleOwner(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/navigation/NavController;->setOnBackPressedDispatcher(Landroidx/activity/OnBackPressedDispatcher;)V
HSPLandroidx/navigation/NavController;->setViewModelStore(Landroidx/lifecycle/ViewModelStore;)V
HSPLandroidx/navigation/NavController;->updateBackStackLifecycle$navigation_runtime_release()V
HSPLandroidx/navigation/NavController;->updateOnBackPressedCallbackEnabled()V
HSPLandroidx/navigation/NavControllerViewModel$Companion$FACTORY$1;-><init>()V
HSPLandroidx/navigation/NavControllerViewModel$Companion$FACTORY$1;->create(Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/navigation/NavControllerViewModel$Companion;-><init>()V
HSPLandroidx/navigation/NavControllerViewModel$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavControllerViewModel$Companion;->getInstance(Landroidx/lifecycle/ViewModelStore;)Landroidx/navigation/NavControllerViewModel;
HSPLandroidx/navigation/NavControllerViewModel;-><clinit>()V
HSPLandroidx/navigation/NavControllerViewModel;-><init>()V
HSPLandroidx/navigation/NavControllerViewModel;->access$getFACTORY$cp()Landroidx/lifecycle/ViewModelProvider$Factory;
HSPLandroidx/navigation/NavHostController;-><init>(Landroid/content/Context;)V
HSPLandroidx/navigation/NavHostController;->enableOnBackPressed(Z)V
HSPLandroidx/navigation/NavHostController;->setLifecycleOwner(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/navigation/NavHostController;->setOnBackPressedDispatcher(Landroidx/activity/OnBackPressedDispatcher;)V
HSPLandroidx/navigation/NavHostController;->setViewModelStore(Landroidx/lifecycle/ViewModelStore;)V
HSPLandroidx/navigation/NavInflater$Companion;-><init>()V
HSPLandroidx/navigation/NavInflater$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/navigation/NavInflater;-><clinit>()V
HSPLandroidx/navigation/NavInflater;-><init>(Landroid/content/Context;Landroidx/navigation/NavigatorProvider;)V
HSPLandroidx/navigation/NavInflater;->inflate(I)Landroidx/navigation/NavGraph;
HSPLandroidx/navigation/NavInflater;->inflate(Landroid/content/res/Resources;Landroid/content/res/XmlResourceParser;Landroid/util/AttributeSet;I)Landroidx/navigation/NavDestination;
HSPLandroidx/navigation/NavInflater;->inflateAction(Landroid/content/res/Resources;Landroidx/navigation/NavDestination;Landroid/util/AttributeSet;Landroid/content/res/XmlResourceParser;I)V
HSPLandroidx/navigation/NavInflater;->inflateArgument(Landroid/content/res/TypedArray;Landroid/content/res/Resources;I)Landroidx/navigation/NavArgument;
HSPLandroidx/navigation/NavInflater;->inflateArgumentForDestination(Landroid/content/res/Resources;Landroidx/navigation/NavDestination;Landroid/util/AttributeSet;I)V
HSPLandroidx/navigation/Navigation;-><clinit>()V
HSPLandroidx/navigation/Navigation;-><init>()V
HSPLandroidx/navigation/Navigation;->setViewNavController(Landroid/view/View;Landroidx/navigation/NavController;)V
Landroidx/navigation/ActivityNavigator$Companion;
Landroidx/navigation/ActivityNavigator$hostActivity$1;
Landroidx/navigation/ActivityNavigator;
Landroidx/navigation/NavController$Companion;
Landroidx/navigation/NavController$NavControllerNavigatorState;
Landroidx/navigation/NavController$activity$1;
Landroidx/navigation/NavController$lifecycleObserver$1;
Landroidx/navigation/NavController$navInflater$2;
Landroidx/navigation/NavController$navigate$4;
Landroidx/navigation/NavController$onBackPressedCallback$1;
Landroidx/navigation/NavController;
Landroidx/navigation/NavControllerViewModel$Companion$FACTORY$1;
Landroidx/navigation/NavControllerViewModel$Companion;
Landroidx/navigation/NavControllerViewModel;
Landroidx/navigation/NavHost;
Landroidx/navigation/NavHostController;
Landroidx/navigation/NavInflater$Companion;
Landroidx/navigation/NavInflater;
Landroidx/navigation/Navigation;
PLandroidx/navigation/NavControllerViewModel;->onCleared()V
HSPLandroidx/navigation/R$styleable;-><clinit>()V
Landroidx/navigation/R$id;
Landroidx/navigation/R$styleable;

# Baseline profiles for androidx.activity

HSPLandroidx/activity/ComponentActivity$1;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$2;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$4;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$4;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$5;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$5;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$6;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;->onContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/ComponentActivity;-><init>()V
HSPLandroidx/activity/ComponentActivity;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/ComponentActivity;->ensureViewModelStore()V
HSPLandroidx/activity/ComponentActivity;->getActivityResultRegistry()Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/ComponentActivity;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/activity/ComponentActivity;->getViewModelStore()Landroidx/lifecycle/ViewModelStore;
HSPLandroidx/activity/ComponentActivity;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/activity/OnBackPressedCallback;-><init>(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->addCancellable(Landroidx/activity/Cancellable;)V
HSPLandroidx/activity/OnBackPressedCallback;->remove()V
HSPLandroidx/activity/OnBackPressedCallback;->setEnabled(Z)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/lifecycle/Lifecycle;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;-><init>(Ljava/lang/Runnable;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCancellableCallback(Landroidx/activity/OnBackPressedCallback;)Landroidx/activity/Cancellable;
HSPLandroidx/activity/contextaware/ContextAwareHelper;-><init>()V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->dispatchOnContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/result/ActivityResultLauncher;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry$3;-><init>(Landroidx/activity/result/ActivityResultRegistry;Ljava/lang/String;ILandroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry$CallbackAndContract;-><init>(Landroidx/activity/result/ActivityResultCallback;Landroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry;->bindRcKey(ILjava/lang/String;)V
HSPLandroidx/activity/result/ActivityResultRegistry;->generateRandomNumber()I
HSPLandroidx/activity/result/ActivityResultRegistry;->register(Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;Landroidx/activity/result/ActivityResultCallback;)Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultRegistry;->registerKey(Ljava/lang/String;)I
HSPLandroidx/activity/result/contract/ActivityResultContract;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><init>()V
Landroidx/activity/Cancellable;
Landroidx/activity/ComponentActivity$1;
Landroidx/activity/ComponentActivity$2;
Landroidx/activity/ComponentActivity$3;
Landroidx/activity/ComponentActivity$4;
Landroidx/activity/ComponentActivity$5;
Landroidx/activity/ComponentActivity$6;
Landroidx/activity/ComponentActivity$7;
Landroidx/activity/ComponentActivity$NonConfigurationInstances;
Landroidx/activity/ComponentActivity;
Landroidx/activity/OnBackPressedCallback;
Landroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher;
Landroidx/activity/OnBackPressedDispatcherOwner;
Landroidx/activity/contextaware/ContextAware;
Landroidx/activity/contextaware/ContextAwareHelper;
Landroidx/activity/contextaware/OnContextAvailableListener;
Landroidx/activity/result/ActivityResult;
Landroidx/activity/result/ActivityResultCallback;
Landroidx/activity/result/ActivityResultCaller;
Landroidx/activity/result/ActivityResultLauncher;
Landroidx/activity/result/ActivityResultRegistry$3;
Landroidx/activity/result/ActivityResultRegistry$CallbackAndContract;
Landroidx/activity/result/ActivityResultRegistry;
Landroidx/activity/result/ActivityResultRegistryOwner;
Landroidx/activity/result/contract/ActivityResultContract;
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;
PLandroidx/activity/ComponentActivity$1;->run()V
PLandroidx/activity/ComponentActivity;->access$001(Landroidx/activity/ComponentActivity;)V
PLandroidx/activity/ComponentActivity;->onBackPressed()V
PLandroidx/activity/OnBackPressedCallback;->isEnabled()Z
PLandroidx/activity/OnBackPressedCallback;->removeCancellable(Landroidx/activity/Cancellable;)V
PLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher;->onBackPressed()V
PLandroidx/activity/contextaware/ContextAwareHelper;->clearAvailableContext()V
PLandroidx/activity/result/ActivityResultRegistry$3;->unregister()V
PLandroidx/activity/result/ActivityResultRegistry;->unregister(Ljava/lang/String;)V

Landroidx/activity/Cancellable;
Landroidx/activity/ComponentActivity;
HSPLandroidx/activity/ComponentActivity;-><init>()V
HSPLandroidx/activity/ComponentActivity;-><init>(I)V
HSPLandroidx/activity/ComponentActivity;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/ComponentActivity;->createFullyDrawnExecutor()Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutor;
HSPLandroidx/activity/ComponentActivity;->ensureViewModelStore()V
HSPLandroidx/activity/ComponentActivity;->getActivityResultRegistry()Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/ComponentActivity;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;
HSPLandroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/ComponentActivity;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/activity/ComponentActivity;->getViewModelStore()Landroidx/lifecycle/ViewModelStore;
HSPLandroidx/activity/ComponentActivity;->lambda$new$2$androidx-activity-ComponentActivity(Landroid/content/Context;)V
HSPLandroidx/activity/ComponentActivity;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/activity/ComponentActivity;->onTrimMemory(I)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda0;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda0;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda1;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda1;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda2;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda2;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;->onContextAvailable(Landroid/content/Context;)V
Landroidx/activity/ComponentActivity$1;
HSPLandroidx/activity/ComponentActivity$1;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$2;
HSPLandroidx/activity/ComponentActivity$2;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$3;
HSPLandroidx/activity/ComponentActivity$3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$4;
HSPLandroidx/activity/ComponentActivity$4;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$4;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$5;
HSPLandroidx/activity/ComponentActivity$5;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$5;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$Api33Impl;
HSPLandroidx/activity/ComponentActivity$Api33Impl;->getOnBackInvokedDispatcher(Landroid/app/Activity;)Landroid/window/OnBackInvokedDispatcher;
Landroidx/activity/ComponentActivity$NonConfigurationInstances;
Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutor;
Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl;
HSPLandroidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m$1(Landroid/view/Window;I)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m$1(Landroid/view/Window;Z)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m(Landroid/view/Window;I)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m(Landroid/view/Window;Z)V
Landroidx/activity/EdgeToEdge;
HSPLandroidx/activity/EdgeToEdge;-><clinit>()V
HSPLandroidx/activity/EdgeToEdge;->enable$default(Landroidx/activity/ComponentActivity;Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;ILjava/lang/Object;)V
HSPLandroidx/activity/EdgeToEdge;->enable(Landroidx/activity/ComponentActivity;Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;)V
Landroidx/activity/EdgeToEdgeApi29;
HSPLandroidx/activity/EdgeToEdgeApi29;-><init>()V
HSPLandroidx/activity/EdgeToEdgeApi29;->setUp(Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;Landroid/view/Window;Landroid/view/View;ZZ)V
Landroidx/activity/EdgeToEdgeImpl;
Landroidx/activity/FullyDrawnReporter;
HSPLandroidx/activity/FullyDrawnReporter;-><init>(Ljava/util/concurrent/Executor;Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/FullyDrawnReporter$$ExternalSyntheticLambda0;
HSPLandroidx/activity/FullyDrawnReporter$$ExternalSyntheticLambda0;-><init>(Landroidx/activity/FullyDrawnReporter;)V
Landroidx/activity/FullyDrawnReporterOwner;
Landroidx/activity/OnBackPressedCallback;
HSPLandroidx/activity/OnBackPressedCallback;-><init>(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->addCancellable(Landroidx/activity/Cancellable;)V
HSPLandroidx/activity/OnBackPressedCallback;->isEnabled()Z
HSPLandroidx/activity/OnBackPressedCallback;->setEnabled(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->setEnabledChangedCallback$activity_release(Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/OnBackPressedDispatcher;-><init>(Ljava/lang/Runnable;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCancellableCallback$activity_release(Landroidx/activity/OnBackPressedCallback;)Landroidx/activity/Cancellable;
HSPLandroidx/activity/OnBackPressedDispatcher;->hasEnabledCallbacks()Z
HSPLandroidx/activity/OnBackPressedDispatcher;->setOnBackInvokedDispatcher(Landroid/window/OnBackInvokedDispatcher;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->updateBackInvokedCallbackState$activity_release()V
Landroidx/activity/OnBackPressedDispatcher$1;
HSPLandroidx/activity/OnBackPressedDispatcher$1;-><init>(Landroidx/activity/OnBackPressedDispatcher;)V
HSPLandroidx/activity/OnBackPressedDispatcher$1;->invoke()Ljava/lang/Object;
HSPLandroidx/activity/OnBackPressedDispatcher$1;->invoke()V
Landroidx/activity/OnBackPressedDispatcher$2;
HSPLandroidx/activity/OnBackPressedDispatcher$2;-><init>(Landroidx/activity/OnBackPressedDispatcher;)V
Landroidx/activity/OnBackPressedDispatcher$Api33Impl;
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;-><clinit>()V
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;-><init>()V
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;->createOnBackInvokedCallback(Lkotlin/jvm/functions/Function0;)Landroid/window/OnBackInvokedCallback;
Landroidx/activity/OnBackPressedDispatcher$Api33Impl$$ExternalSyntheticLambda0;
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl$$ExternalSyntheticLambda0;-><init>(Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/lifecycle/Lifecycle;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;
HSPLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/activity/OnBackPressedCallback;)V
Landroidx/activity/OnBackPressedDispatcherOwner;
Landroidx/activity/R$id;
Landroidx/activity/SystemBarStyle;
HSPLandroidx/activity/SystemBarStyle;-><clinit>()V
HSPLandroidx/activity/SystemBarStyle;-><init>(III)V
HSPLandroidx/activity/SystemBarStyle;-><init>(IIILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/activity/SystemBarStyle;->getNightMode$activity_release()I
HSPLandroidx/activity/SystemBarStyle;->getScrimWithEnforcedContrast$activity_release(Z)I
HSPLandroidx/activity/SystemBarStyle;->isDark$activity_release(Landroid/content/res/Resources;)Z
Landroidx/activity/SystemBarStyle$Companion;
HSPLandroidx/activity/SystemBarStyle$Companion;-><init>()V
HSPLandroidx/activity/SystemBarStyle$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/activity/SystemBarStyle$Companion;->auto(II)Landroidx/activity/SystemBarStyle;
Landroidx/activity/ViewTreeOnBackPressedDispatcherOwner;
HSPLandroidx/activity/ViewTreeOnBackPressedDispatcherOwner;->set(Landroid/view/View;Landroidx/activity/OnBackPressedDispatcherOwner;)V
Landroidx/activity/contextaware/ContextAware;
Landroidx/activity/contextaware/ContextAwareHelper;
HSPLandroidx/activity/contextaware/ContextAwareHelper;-><init>()V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->dispatchOnContextAvailable(Landroid/content/Context;)V
Landroidx/activity/contextaware/OnContextAvailableListener;
Landroidx/activity/result/ActivityResult;
Landroidx/activity/result/ActivityResultCallback;
Landroidx/activity/result/ActivityResultCaller;
Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultLauncher;-><init>()V
Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/result/ActivityResultRegistry;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry;->bindRcKey(ILjava/lang/String;)V
HSPLandroidx/activity/result/ActivityResultRegistry;->generateRandomNumber()I
HSPLandroidx/activity/result/ActivityResultRegistry;->register(Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;Landroidx/activity/result/ActivityResultCallback;)Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultRegistry;->registerKey(Ljava/lang/String;)V
Landroidx/activity/result/ActivityResultRegistry$3;
HSPLandroidx/activity/result/ActivityResultRegistry$3;-><init>(Landroidx/activity/result/ActivityResultRegistry;Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;)V
Landroidx/activity/result/ActivityResultRegistry$CallbackAndContract;
HSPLandroidx/activity/result/ActivityResultRegistry$CallbackAndContract;-><init>(Landroidx/activity/result/ActivityResultCallback;Landroidx/activity/result/contract/ActivityResultContract;)V
Landroidx/activity/result/ActivityResultRegistryOwner;
Landroidx/activity/result/contract/ActivityResultContract;
HSPLandroidx/activity/result/contract/ActivityResultContract;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><clinit>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><clinit>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
# Baseline profile rules for androidx.compose.material
# =============================================

HSPLandroidx/compose/material/CardKt;->**(**)**
HSPLandroidx/compose/material/Colors;->**(**)**
HSPLandroidx/compose/material/ColorsKt**->**(**)**
HSPLandroidx/compose/material/CheckDrawingCache;->**(**)**
HSPLandroidx/compose/material/CheckboxDefaults;->**(**)**
HSPLandroidx/compose/material/DefaultCheckboxColors;->**(**)**
HSPLandroidx/compose/material/CheckboxKt**->**(**)**
HSPLandroidx/compose/material/ButtonKt**->**(**)**
HSPLandroidx/compose/material/AppBarKt**->**(**)**
HSPLandroidx/compose/material/ProgressIndicatorKt;->**(**)**
HSPLandroidx/compose/material/IconKt;->**(**)**
HSPLandroidx/compose/material/ContentAlpha**->**(**)**
HSPLandroidx/compose/material/ContentColorKt**->**(**)**
HSPLandroidx/compose/material/ComposableSingletons$**->**(**)**
HSPLandroidx/compose/material/DefaultElevationOverlay;->**(**)**
HSPLandroidx/compose/material/ElevationOverlayKt**->**(**)**
HSPLandroidx/compose/material/DrawerKt;->**(**)**
HSPLandroidx/compose/material/DrawerState;->**(**)**
HSPLandroidx/compose/material/FadeInFadeOutState;->**(**)**
HSPLandroidx/compose/material/FabPosition;->**(**)**
HSPLandroidx/compose/material/FloatingActionButtonKt**->**(**)**
HSPLandroidx/compose/material/MaterialTheme;->**(**)**
HSPLandroidx/compose/material/MinimumTouchTargetModifier;->**(**)**
HSPLandroidx/compose/material/MaterialTextSelectionColorsKt;->**(**)**
HSPLandroidx/compose/material/MaterialRippleTheme;->**(**)**
HSPLandroidx/compose/material/ScaffoldKt**->**(**)**
HSPLandroidx/compose/material/Shapes**->**(**)**
HSPLandroidx/compose/material/SnackbarHostKt**->**(**)**
HSPLandroidx/compose/material/SurfaceKt**->**(**)**
HSPLandroidx/compose/material/SwipeableKt**->**(**)**
HSPLandroidx/compose/material/SwipeableState**->**(**)**
HSPLandroidx/compose/material/SwitchDefaults;->**(**)**
HSPLandroidx/compose/material/TextKt**->**(**)**
HSPLandroidx/compose/material/TouchTargetKt**->**(**)**
HSPLandroidx/compose/material/Typography;->**(**)**

# Prioritize only the top level classes for material. Notably, this ignores icons

Landroidx/compose/material/*;

# Baseline profile rules for androidx.compose.material3
# =============================================

HSPLandroidx/compose/material3/CardColors;->**(**)**
HSPLandroidx/compose/material3/CardElevation;->**(**)**
HSPLandroidx/compose/material3/CardKt**->**(**)**
HSPLandroidx/compose/material3/CheckDrawingCache;->**(**)**
HSPLandroidx/compose/material3/CheckboxColors;->**(**)**
HSPLandroidx/compose/material3/CheckboxKt**->**(**)**
HSPLandroidx/compose/material3/ColorScheme**->**(**)**
HSPLandroidx/compose/material3/ContentColorKt;->**(**)**
HSPLandroidx/compose/material3/DefaultPlatformTextStyle_androidKt;->**(**)**
HSPLandroidx/compose/material3/InteractiveComponentSizeKt;->**(**)**
HSPLandroidx/compose/material3/MinimumInteractiveComponentSizeModifier**->**(**)**
HSPLandroidx/compose/material3/ShapeDefaults;->**(**)**
HSPLandroidx/compose/material3/Shapes;->**(**)**
HSPLandroidx/compose/material3/ShapesKt**->**(**)**
HSPLandroidx/compose/material3/SurfaceKt**->**(**)**
HSPLandroidx/compose/material3/TextKt**->**(**)**
HSPLandroidx/compose/material3/CheckboxTokens;->**(**)**
HSPLandroidx/compose/material3/ColorDarkTokens;->**(**)**
HSPLandroidx/compose/material3/ColorLightTokens;->**(**)**
HSPLandroidx/compose/material3/ElevationTokens;->**(**)**
HSPLandroidx/compose/material3/FilledCardTokens;->**(**)**
HSPLandroidx/compose/material3/PaletteTokens;->**(**)**
HSPLandroidx/compose/material3/ShapeTokens;->**(**)**
HSPLandroidx/compose/material3/TypographyTokens;->**(**)**

Lcoil3/compose/AsyncImageKt;
SPLcoil3/compose/AsyncImageKt;->AsyncImage-76YX9Dk(Lcoil3/compose/internal/AsyncImageState;Ljava/lang/String;Landroidx/compose/ui/Modifier;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;IZLandroidx/compose/runtime/Composer;II)V
SPLcoil3/compose/AsyncImageKt;->AsyncImage-nc27qi8(Ljava/lang/Object;Ljava/lang/String;Lcoil3/ImageLoader;Landroidx/compose/ui/Modifier;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;IZLandroidx/compose/runtime/Composer;III)V
Lcoil3/compose/AsyncImageModelEqualityDelegate;
SPLcoil3/compose/AsyncImageModelEqualityDelegate;-><clinit>()V
Lcoil3/compose/AsyncImageModelEqualityDelegate$Companion;
SPLcoil3/compose/AsyncImageModelEqualityDelegate$Companion;-><clinit>()V
SPLcoil3/compose/AsyncImageModelEqualityDelegate$Companion;-><init>()V
Lcoil3/compose/AsyncImageModelEqualityDelegate$Companion$AllProperties$1;
SPLcoil3/compose/AsyncImageModelEqualityDelegate$Companion$AllProperties$1;-><init>()V
Lcoil3/compose/AsyncImageModelEqualityDelegate$Companion$Default$1;
SPLcoil3/compose/AsyncImageModelEqualityDelegate$Companion$Default$1;-><init>()V
SPLcoil3/compose/AsyncImageModelEqualityDelegate$Companion$Default$1;->equals(Ljava/lang/Object;Ljava/lang/Object;)Z
Lcoil3/compose/AsyncImagePainter;
SPLcoil3/compose/AsyncImagePainter;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter;-><init>(Lcoil3/compose/AsyncImagePainter$Input;)V
SPLcoil3/compose/AsyncImagePainter;->access$toState(Lcoil3/compose/AsyncImagePainter;Lcoil3/request/ImageResult;)Lcoil3/compose/AsyncImagePainter$State;
SPLcoil3/compose/AsyncImagePainter;->access$updateRequest(Lcoil3/compose/AsyncImagePainter;Lcoil3/request/ImageRequest;Z)Lcoil3/request/ImageRequest;
SPLcoil3/compose/AsyncImagePainter;->access$updateState(Lcoil3/compose/AsyncImagePainter;Lcoil3/compose/AsyncImagePainter$State;)V
SPLcoil3/compose/AsyncImagePainter;->getIntrinsicSize-NH-jbRc()J
HSPLcoil3/compose/AsyncImagePainter;->getPainter()Landroidx/compose/ui/graphics/painter/Painter;
SPLcoil3/compose/AsyncImagePainter;->getPreviewHandler$coil_compose_core_release()Lcoil3/compose/AsyncImagePreviewHandler;
SPLcoil3/compose/AsyncImagePainter;->getScope$coil_compose_core_release()Lkotlinx/coroutines/CoroutineScope;
SPLcoil3/compose/AsyncImagePainter;->launchJob()V
HSPLcoil3/compose/AsyncImagePainter;->onDraw(Landroidx/compose/ui/graphics/drawscope/DrawScope;)V
SPLcoil3/compose/AsyncImagePainter;->onForgotten()V
SPLcoil3/compose/AsyncImagePainter;->onRemembered()V
SPLcoil3/compose/AsyncImagePainter;->restart()V
SPLcoil3/compose/AsyncImagePainter;->setContentScale$coil_compose_core_release(Landroidx/compose/ui/layout/ContentScale;)V
SPLcoil3/compose/AsyncImagePainter;->setDrawSize-uvyYCjk(J)V
SPLcoil3/compose/AsyncImagePainter;->setFilterQuality-vDHp3xo$coil_compose_core_release(I)V
SPLcoil3/compose/AsyncImagePainter;->setOnState$coil_compose_core_release(Lkotlin/jvm/functions/Function1;)V
SPLcoil3/compose/AsyncImagePainter;->setPainter(Landroidx/compose/ui/graphics/painter/Painter;)V
SPLcoil3/compose/AsyncImagePainter;->setPreviewHandler$coil_compose_core_release(Lcoil3/compose/AsyncImagePreviewHandler;)V
SPLcoil3/compose/AsyncImagePainter;->setRememberJob(Lkotlinx/coroutines/Job;)V
SPLcoil3/compose/AsyncImagePainter;->setScope$coil_compose_core_release(Lkotlinx/coroutines/CoroutineScope;)V
SPLcoil3/compose/AsyncImagePainter;->setTransform$coil_compose_core_release(Lkotlin/jvm/functions/Function1;)V
SPLcoil3/compose/AsyncImagePainter;->set_input$coil_compose_core_release(Lcoil3/compose/AsyncImagePainter$Input;)V
SPLcoil3/compose/AsyncImagePainter;->toState(Lcoil3/request/ImageResult;)Lcoil3/compose/AsyncImagePainter$State;
SPLcoil3/compose/AsyncImagePainter;->updateRequest(Lcoil3/request/ImageRequest;Z)Lcoil3/request/ImageRequest;
SPLcoil3/compose/AsyncImagePainter;->updateState(Lcoil3/compose/AsyncImagePainter$State;)V
Lcoil3/compose/AsyncImagePainter$$ExternalSyntheticLambda0;
SPLcoil3/compose/AsyncImagePainter$$ExternalSyntheticLambda0;-><init>()V
Lcoil3/compose/AsyncImagePainter$Companion;
SPLcoil3/compose/AsyncImagePainter$Companion;-><init>()V
SPLcoil3/compose/AsyncImagePainter$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/compose/AsyncImagePainter$Input;
SPLcoil3/compose/AsyncImagePainter$Input;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter$Input;-><init>(Lcoil3/ImageLoader;Lcoil3/request/ImageRequest;Lcoil3/compose/AsyncImageModelEqualityDelegate;)V
SPLcoil3/compose/AsyncImagePainter$Input;->equals(Ljava/lang/Object;)Z
SPLcoil3/compose/AsyncImagePainter$Input;->getImageLoader()Lcoil3/ImageLoader;
SPLcoil3/compose/AsyncImagePainter$Input;->getRequest()Lcoil3/request/ImageRequest;
Lcoil3/compose/AsyncImagePainter$State;
Lcoil3/compose/AsyncImagePainter$State$Empty;
SPLcoil3/compose/AsyncImagePainter$State$Empty;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter$State$Empty;-><init>()V
SPLcoil3/compose/AsyncImagePainter$State$Empty;->equals(Ljava/lang/Object;)Z
SPLcoil3/compose/AsyncImagePainter$State$Empty;->getPainter()Landroidx/compose/ui/graphics/painter/Painter;
Lcoil3/compose/AsyncImagePainter$State$Error;
Lcoil3/compose/AsyncImagePainter$State$Loading;
SPLcoil3/compose/AsyncImagePainter$State$Loading;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter$State$Loading;-><init>(Landroidx/compose/ui/graphics/painter/Painter;)V
SPLcoil3/compose/AsyncImagePainter$State$Loading;->copy(Landroidx/compose/ui/graphics/painter/Painter;)Lcoil3/compose/AsyncImagePainter$State$Loading;
SPLcoil3/compose/AsyncImagePainter$State$Loading;->equals(Ljava/lang/Object;)Z
SPLcoil3/compose/AsyncImagePainter$State$Loading;->getPainter()Landroidx/compose/ui/graphics/painter/Painter;
Lcoil3/compose/AsyncImagePainter$State$Success;
SPLcoil3/compose/AsyncImagePainter$State$Success;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter$State$Success;-><init>(Landroidx/compose/ui/graphics/painter/Painter;Lcoil3/request/SuccessResult;)V
SPLcoil3/compose/AsyncImagePainter$State$Success;->equals(Ljava/lang/Object;)Z
SPLcoil3/compose/AsyncImagePainter$State$Success;->getPainter()Landroidx/compose/ui/graphics/painter/Painter;
SPLcoil3/compose/AsyncImagePainter$State$Success;->getResult()Lcoil3/request/SuccessResult;
Lcoil3/compose/AsyncImagePainter$launchJob$1;
SPLcoil3/compose/AsyncImagePainter$launchJob$1;-><init>(Lcoil3/compose/AsyncImagePainter;Lcoil3/compose/AsyncImagePainter$Input;Lkotlin/coroutines/Continuation;)V
SPLcoil3/compose/AsyncImagePainter$launchJob$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
SPLcoil3/compose/AsyncImagePainter$launchJob$1;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
SPLcoil3/compose/AsyncImagePainter$launchJob$1;->invoke(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/compose/AsyncImagePainter$launchJob$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/AsyncImagePainter$updateRequest$$inlined$target$default$1;
SPLcoil3/compose/AsyncImagePainter$updateRequest$$inlined$target$default$1;-><init>(Lcoil3/request/ImageRequest;Lcoil3/compose/AsyncImagePainter;)V
SPLcoil3/compose/AsyncImagePainter$updateRequest$$inlined$target$default$1;->onStart(Lcoil3/Image;)V
SPLcoil3/compose/AsyncImagePainter$updateRequest$$inlined$target$default$1;->onSuccess(Lcoil3/Image;)V
Lcoil3/compose/AsyncImagePainter_androidKt;
SPLcoil3/compose/AsyncImagePainter_androidKt;-><clinit>()V
SPLcoil3/compose/AsyncImagePainter_androidKt;->maybeNewCrossfadePainter(Lcoil3/compose/AsyncImagePainter$State;Lcoil3/compose/AsyncImagePainter$State;Landroidx/compose/ui/layout/ContentScale;)Lcoil3/compose/CrossfadePainter;
Lcoil3/compose/AsyncImagePainter_androidKt$FakeTransitionTarget$1;
SPLcoil3/compose/AsyncImagePainter_androidKt$FakeTransitionTarget$1;-><init>()V
Lcoil3/compose/AsyncImagePreviewHandler;
Lcoil3/compose/ConstraintsSizeResolver;
SPLcoil3/compose/ConstraintsSizeResolver;-><clinit>()V
SPLcoil3/compose/ConstraintsSizeResolver;-><init>()V
SPLcoil3/compose/ConstraintsSizeResolver;->access$getContinuations$p(Lcoil3/compose/ConstraintsSizeResolver;)Ljava/util/List;
SPLcoil3/compose/ConstraintsSizeResolver;->setConstraints-BRTryo0(J)V
SPLcoil3/compose/ConstraintsSizeResolver;->size(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
Lcoil3/compose/ConstraintsSizeResolver$size$1;
SPLcoil3/compose/ConstraintsSizeResolver$size$1;-><init>(Lcoil3/compose/ConstraintsSizeResolver;Lkotlin/coroutines/Continuation;)V
SPLcoil3/compose/ConstraintsSizeResolver$size$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/CrossfadePainter;
SPLcoil3/compose/CrossfadePainter;-><clinit>()V
SPLcoil3/compose/CrossfadePainter;-><init>(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/layout/ContentScale;JLkotlin/time/TimeSource;ZZ)V
SPLcoil3/compose/CrossfadePainter;-><init>(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/layout/ContentScale;JLkotlin/time/TimeSource;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/compose/CrossfadePainter;-><init>(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/layout/ContentScale;JLkotlin/time/TimeSource;ZZLkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/compose/CrossfadePainter;->computeDrawSize-x8L_9b0(JJ)J
HSPLcoil3/compose/CrossfadePainter;->computeIntrinsicSize-NH-jbRc()J
HSPLcoil3/compose/CrossfadePainter;->drawPainter(Landroidx/compose/ui/graphics/drawscope/DrawScope;Landroidx/compose/ui/graphics/painter/Painter;F)V
SPLcoil3/compose/CrossfadePainter;->getIntrinsicSize-NH-jbRc()J
SPLcoil3/compose/CrossfadePainter;->getInvalidateTick()I
HSPLcoil3/compose/CrossfadePainter;->onDraw(Landroidx/compose/ui/graphics/drawscope/DrawScope;)V
SPLcoil3/compose/CrossfadePainter;->setInvalidateTick(I)V
Lcoil3/compose/DrawScopeSizeResolver;
Lcoil3/compose/ImagePainter_androidKt;
SPLcoil3/compose/ImagePainter_androidKt;->asPainter-55t9-rM(Lcoil3/Image;Landroid/content/Context;I)Landroidx/compose/ui/graphics/painter/Painter;
Lcoil3/compose/LocalAsyncImageModelEqualityDelegateKt;
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt;->$r8$lambda$VRknrw8yX_-MCykBuYBHq2_U2-E()Lcoil3/compose/AsyncImageModelEqualityDelegate;
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt;-><clinit>()V
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt;->LocalAsyncImageModelEqualityDelegate$lambda$0()Lcoil3/compose/AsyncImageModelEqualityDelegate;
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt;->getLocalAsyncImageModelEqualityDelegate()Landroidx/compose/runtime/ProvidableCompositionLocal;
Lcoil3/compose/LocalAsyncImageModelEqualityDelegateKt$$ExternalSyntheticLambda0;
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt$$ExternalSyntheticLambda0;-><init>()V
SPLcoil3/compose/LocalAsyncImageModelEqualityDelegateKt$$ExternalSyntheticLambda0;->invoke()Ljava/lang/Object;
Lcoil3/compose/SingletonAsyncImageKt;
SPLcoil3/compose/SingletonAsyncImageKt;->AsyncImage-x1rPTaM(Ljava/lang/Object;Ljava/lang/String;Landroidx/compose/ui/Modifier;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Landroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;IZLandroidx/compose/runtime/Composer;III)V
Lcoil3/compose/SingletonImageLoadersKt;
SPLcoil3/compose/SingletonImageLoadersKt;->setSingletonImageLoaderFactory(Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/Composer;I)V
Lcoil3/compose/SingletonImageLoadersKt$sam$coil3_SingletonImageLoader_Factory$0;
SPLcoil3/compose/SingletonImageLoadersKt$sam$coil3_SingletonImageLoader_Factory$0;-><init>(Lkotlin/jvm/functions/Function1;)V
SPLcoil3/compose/SingletonImageLoadersKt$sam$coil3_SingletonImageLoader_Factory$0;->newImageLoader(Landroid/content/Context;)Lcoil3/ImageLoader;
Lcoil3/compose/internal/AbstractContentPainterNode;
SPLcoil3/compose/internal/AbstractContentPainterNode;->$r8$lambda$fNHFmpVwvQrDZxTrdgBJ2JxeEWE(Landroidx/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/Unit;
SPLcoil3/compose/internal/AbstractContentPainterNode;-><clinit>()V
SPLcoil3/compose/internal/AbstractContentPainterNode;-><init>(Landroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;ZLjava/lang/String;Lcoil3/compose/ConstraintsSizeResolver;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->applySemantics(Landroidx/compose/ui/semantics/SemanticsPropertyReceiver;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->calculateScaledSize-E7KxVPU(J)J
HSPLcoil3/compose/internal/AbstractContentPainterNode;->draw(Landroidx/compose/ui/graphics/drawscope/ContentDrawScope;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->getConstraintSizeResolver()Lcoil3/compose/ConstraintsSizeResolver;
SPLcoil3/compose/internal/AbstractContentPainterNode;->getContentDescription()Ljava/lang/String;
SPLcoil3/compose/internal/AbstractContentPainterNode;->getShouldAutoInvalidate()Z
SPLcoil3/compose/internal/AbstractContentPainterNode;->getShouldClearDescendantSemantics()Z
SPLcoil3/compose/internal/AbstractContentPainterNode;->getShouldMergeDescendantSemantics()Z
SPLcoil3/compose/internal/AbstractContentPainterNode;->measure-3p2s80s(Landroidx/compose/ui/layout/MeasureScope;Landroidx/compose/ui/layout/Measurable;J)Landroidx/compose/ui/layout/MeasureResult;
SPLcoil3/compose/internal/AbstractContentPainterNode;->measure_3p2s80s$lambda$0(Landroidx/compose/ui/layout/Placeable;Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/Unit;
SPLcoil3/compose/internal/AbstractContentPainterNode;->modifyConstraints-ZezNO4M(J)J
SPLcoil3/compose/internal/AbstractContentPainterNode;->onMeasureResultChanged()V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setAlignment(Landroidx/compose/ui/Alignment;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setAlpha(F)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setClipToBounds(Z)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setColorFilter(Landroidx/compose/ui/graphics/ColorFilter;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setConstraintSizeResolver(Lcoil3/compose/ConstraintsSizeResolver;)V
SPLcoil3/compose/internal/AbstractContentPainterNode;->setContentScale(Landroidx/compose/ui/layout/ContentScale;)V
Lcoil3/compose/internal/AbstractContentPainterNode$$ExternalSyntheticLambda0;
SPLcoil3/compose/internal/AbstractContentPainterNode$$ExternalSyntheticLambda0;-><init>(Landroidx/compose/ui/layout/Placeable;)V
SPLcoil3/compose/internal/AbstractContentPainterNode$$ExternalSyntheticLambda0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/internal/AsyncImageState;
SPLcoil3/compose/internal/AsyncImageState;-><clinit>()V
SPLcoil3/compose/internal/AsyncImageState;-><init>(Ljava/lang/Object;Lcoil3/compose/AsyncImageModelEqualityDelegate;Lcoil3/ImageLoader;)V
SPLcoil3/compose/internal/AsyncImageState;->getImageLoader()Lcoil3/ImageLoader;
SPLcoil3/compose/internal/AsyncImageState;->getModel()Ljava/lang/Object;
SPLcoil3/compose/internal/AsyncImageState;->getModelEqualityDelegate()Lcoil3/compose/AsyncImageModelEqualityDelegate;
Lcoil3/compose/internal/ContentPainterElement;
SPLcoil3/compose/internal/ContentPainterElement;-><clinit>()V
SPLcoil3/compose/internal/ContentPainterElement;-><init>(Lcoil3/request/ImageRequest;Lcoil3/ImageLoader;Lcoil3/compose/AsyncImageModelEqualityDelegate;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILandroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;ZLcoil3/compose/AsyncImagePreviewHandler;Ljava/lang/String;)V
SPLcoil3/compose/internal/ContentPainterElement;-><init>(Lcoil3/request/ImageRequest;Lcoil3/ImageLoader;Lcoil3/compose/AsyncImageModelEqualityDelegate;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILandroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;ZLcoil3/compose/AsyncImagePreviewHandler;Ljava/lang/String;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/compose/internal/ContentPainterElement;->create()Landroidx/compose/ui/Modifier$Node;
SPLcoil3/compose/internal/ContentPainterElement;->create()Lcoil3/compose/internal/ContentPainterNode;
SPLcoil3/compose/internal/ContentPainterElement;->equals(Ljava/lang/Object;)Z
SPLcoil3/compose/internal/ContentPainterElement;->update(Landroidx/compose/ui/Modifier$Node;)V
SPLcoil3/compose/internal/ContentPainterElement;->update(Lcoil3/compose/internal/ContentPainterNode;)V
Lcoil3/compose/internal/ContentPainterNode;
SPLcoil3/compose/internal/ContentPainterNode;-><clinit>()V
SPLcoil3/compose/internal/ContentPainterNode;-><init>(Lcoil3/compose/AsyncImagePainter;Landroidx/compose/ui/Alignment;Landroidx/compose/ui/layout/ContentScale;FLandroidx/compose/ui/graphics/ColorFilter;ZLjava/lang/String;Lcoil3/compose/ConstraintsSizeResolver;)V
SPLcoil3/compose/internal/ContentPainterNode;->getPainter()Landroidx/compose/ui/graphics/painter/Painter;
SPLcoil3/compose/internal/ContentPainterNode;->getPainter()Lcoil3/compose/AsyncImagePainter;
SPLcoil3/compose/internal/ContentPainterNode;->onAttach()V
SPLcoil3/compose/internal/ContentPainterNode;->onDetach()V
SPLcoil3/compose/internal/ContentPainterNode;->onReset()V
Lcoil3/compose/internal/DeferredDispatchCoroutineContext;
SPLcoil3/compose/internal/DeferredDispatchCoroutineContext;-><init>(Lkotlin/coroutines/CoroutineContext;)V
SPLcoil3/compose/internal/DeferredDispatchCoroutineContext;->newContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;)Lcoil3/compose/internal/ForwardingCoroutineContext;
Lcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;-><clinit>()V
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;-><init>(Lkotlinx/coroutines/CoroutineDispatcher;)V
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;->dispatch(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Runnable;)V
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;->getCurrentDispatcher()Lkotlinx/coroutines/CoroutineDispatcher;
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;->get_unconfined$volatile$FU()Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;->isDispatchNeeded(Lkotlin/coroutines/CoroutineContext;)Z
SPLcoil3/compose/internal/DeferredDispatchCoroutineDispatcher;->setUnconfined(Z)V
Lcoil3/compose/internal/DeferredDispatchKt;
SPLcoil3/compose/internal/DeferredDispatchKt;->launchWithDeferredDispatch(Lkotlinx/coroutines/CoroutineScope;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/Job;
Lcoil3/compose/internal/ForwardingCoroutineContext;
SPLcoil3/compose/internal/ForwardingCoroutineContext;-><clinit>()V
SPLcoil3/compose/internal/ForwardingCoroutineContext;-><init>(Lkotlin/coroutines/CoroutineContext;)V
SPLcoil3/compose/internal/ForwardingCoroutineContext;->fold(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
SPLcoil3/compose/internal/ForwardingCoroutineContext;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
SPLcoil3/compose/internal/ForwardingCoroutineContext;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;
Lcoil3/compose/internal/UtilsKt;
SPLcoil3/compose/internal/UtilsKt;->$r8$lambda$DWgX8zB5UP28SEDzF0YMQUhwIG4(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/compose/AsyncImagePainter$State;)Lkotlin/Unit;
SPLcoil3/compose/internal/UtilsKt;->$r8$lambda$u7X2A7DhL8n_2ziBl217zYQqpuc(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lcoil3/compose/AsyncImagePainter$State;)Lcoil3/compose/AsyncImagePainter$State;
SPLcoil3/compose/internal/UtilsKt;-><clinit>()V
SPLcoil3/compose/internal/UtilsKt;->getDispatcher(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineDispatcher;
SPLcoil3/compose/internal/UtilsKt;->getUseMinConstraintsMeasurePolicy()Landroidx/compose/ui/layout/MeasurePolicy;
SPLcoil3/compose/internal/UtilsKt;->getZeroConstraints()J
SPLcoil3/compose/internal/UtilsKt;->onStateOf$lambda$5(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/compose/AsyncImagePainter$State;)Lkotlin/Unit;
SPLcoil3/compose/internal/UtilsKt;->onStateOf(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Lkotlin/jvm/functions/Function1;
SPLcoil3/compose/internal/UtilsKt;->previewHandler(Landroidx/compose/runtime/Composer;I)Lcoil3/compose/AsyncImagePreviewHandler;
SPLcoil3/compose/internal/UtilsKt;->rememberSizeResolver(Landroidx/compose/ui/layout/ContentScale;Landroidx/compose/runtime/Composer;I)Lcoil3/size/SizeResolver;
SPLcoil3/compose/internal/UtilsKt;->requestOfWithSizeResolver(Ljava/lang/Object;Landroidx/compose/ui/layout/ContentScale;Landroidx/compose/runtime/Composer;I)Lcoil3/request/ImageRequest;
SPLcoil3/compose/internal/UtilsKt;->toDimension(I)Lcoil3/size/Dimension;
HSPLcoil3/compose/internal/UtilsKt;->toIntSize-uvyYCjk(J)J
SPLcoil3/compose/internal/UtilsKt;->toScale(Landroidx/compose/ui/layout/ContentScale;)Lcoil3/size/Scale;
SPLcoil3/compose/internal/UtilsKt;->toSize-BRTryo0(J)Lcoil3/size/Size;
SPLcoil3/compose/internal/UtilsKt;->transformOf$lambda$4(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Lcoil3/compose/AsyncImagePainter$State;)Lcoil3/compose/AsyncImagePainter$State;
SPLcoil3/compose/internal/UtilsKt;->transformOf(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;)Lkotlin/jvm/functions/Function1;
SPLcoil3/compose/internal/UtilsKt;->validateRequest(Lcoil3/request/ImageRequest;)V
Lcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda0;
SPLcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda0;-><init>(Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;Landroidx/compose/ui/graphics/painter/Painter;)V
SPLcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda1;
SPLcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda1;-><init>(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
SPLcoil3/compose/internal/UtilsKt$$ExternalSyntheticLambda1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;->$r8$lambda$_LBiQfsKeY_K6W3mclBEXttrjRk(Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/Unit;
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;-><clinit>()V
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;-><init>()V
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;->measure-3p2s80s(Landroidx/compose/ui/layout/MeasureScope;Ljava/util/List;J)Landroidx/compose/ui/layout/MeasureResult;
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1;->measure_3p2s80s$lambda$0(Landroidx/compose/ui/layout/Placeable$PlacementScope;)Lkotlin/Unit;
Lcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1$$ExternalSyntheticLambda0;
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1$$ExternalSyntheticLambda0;-><init>()V
SPLcoil3/compose/internal/UtilsKt$UseMinConstraintsMeasurePolicy$1$$ExternalSyntheticLambda0;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/compose/internal/Utils_androidKt;
SPLcoil3/compose/internal/Utils_androidKt;->validateRequestProperties(Lcoil3/request/ImageRequest;)V
# Baseline profiles for lifecycle-livedata-core

HSPLandroidx/lifecycle/LiveData$1;-><init>(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/LiveData$1;->run()V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->activeStateChanged(Z)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->detachObserver()V
HSPLandroidx/lifecycle/LiveData;-><clinit>()V
HSPLandroidx/lifecycle/LiveData;-><init>()V
HSPLandroidx/lifecycle/LiveData;->assertMainThread(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LiveData;->changeActiveCounter(I)V
HSPLandroidx/lifecycle/LiveData;->considerNotify(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->dispatchingValue(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/LiveData;->getVersion()I
HSPLandroidx/lifecycle/LiveData;->hasActiveObservers()Z
HSPLandroidx/lifecycle/LiveData;->observe(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->observeForever(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->onActive()V
HSPLandroidx/lifecycle/LiveData;->onInactive()V
HSPLandroidx/lifecycle/LiveData;->postValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/LiveData;->removeObserver(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->setValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->onChanged(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->plug()V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->unplug()V
HSPLandroidx/lifecycle/MediatorLiveData;-><init>()V
HSPLandroidx/lifecycle/MediatorLiveData;->addSource(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData;->onActive()V
HSPLandroidx/lifecycle/MediatorLiveData;->onInactive()V
HSPLandroidx/lifecycle/MediatorLiveData;->removeSource(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/MutableLiveData;-><init>()V
HSPLandroidx/lifecycle/MutableLiveData;->setValue(Ljava/lang/Object;)V
PLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->detachObserver()V

# Baseline profiles for Lifecycle ViewModel

HSPLandroidx/lifecycle/ViewModel;-><init>()V
HSPLandroidx/lifecycle/ViewModelLazy;-><init>(Lkotlin/reflect/KClass;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/ViewModelProvider;-><init>(Landroidx/lifecycle/ViewModelStore;Landroidx/lifecycle/ViewModelProvider$Factory;)V
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/String;Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;-><init>()V
HSPLandroidx/lifecycle/ViewModelStore;->get(Ljava/lang/String;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;->put(Ljava/lang/String;Landroidx/lifecycle/ViewModel;)V
PLandroidx/lifecycle/ViewModel;->clear()V
PLandroidx/lifecycle/ViewModel;->onCleared()V
PLandroidx/lifecycle/ViewModelStore;->clear()V

Lcoil3/BitmapImage;
SPLcoil3/BitmapImage;-><init>(Landroid/graphics/Bitmap;Z)V
SPLcoil3/BitmapImage;->getBitmap()Landroid/graphics/Bitmap;
SPLcoil3/BitmapImage;->getShareable()Z
SPLcoil3/BitmapImage;->getSize()J
Lcoil3/ColorImage$$ExternalSyntheticBackport0;
SPLcoil3/ColorImage$$ExternalSyntheticBackport0;->m(Ljava/util/concurrent/atomic/AtomicReference;Ljava/lang/Object;Ljava/lang/Object;)Z
Lcoil3/ComponentRegistry;
SPLcoil3/ComponentRegistry;->$r8$lambda$AOMzzwdb7Qz38tc6_CIWPIRh5vs(Lcoil3/ComponentRegistry;)Ljava/util/List;
SPLcoil3/ComponentRegistry;->$r8$lambda$dJAwMCH26B_eSz_vzGP6LqaRwks(Lcoil3/ComponentRegistry;)Ljava/util/List;
SPLcoil3/ComponentRegistry;-><init>()V
SPLcoil3/ComponentRegistry;-><init>(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V
SPLcoil3/ComponentRegistry;-><init>(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/ComponentRegistry;->decoderFactories_delegate$lambda$5(Lcoil3/ComponentRegistry;)Ljava/util/List;
SPLcoil3/ComponentRegistry;->fetcherFactories_delegate$lambda$2(Lcoil3/ComponentRegistry;)Ljava/util/List;
SPLcoil3/ComponentRegistry;->getDecoderFactories()Ljava/util/List;
SPLcoil3/ComponentRegistry;->getFetcherFactories()Ljava/util/List;
SPLcoil3/ComponentRegistry;->getInterceptors()Ljava/util/List;
SPLcoil3/ComponentRegistry;->getKeyers()Ljava/util/List;
SPLcoil3/ComponentRegistry;->getMappers()Ljava/util/List;
SPLcoil3/ComponentRegistry;->map(Ljava/lang/Object;Lcoil3/request/Options;)Ljava/lang/Object;
SPLcoil3/ComponentRegistry;->newBuilder()Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry;->newDecoder(Lcoil3/fetch/SourceFetchResult;Lcoil3/request/Options;Lcoil3/ImageLoader;I)Lkotlin/Pair;
SPLcoil3/ComponentRegistry;->newFetcher(Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/ImageLoader;I)Lkotlin/Pair;
Lcoil3/ComponentRegistry$$ExternalSyntheticLambda0;
SPLcoil3/ComponentRegistry$$ExternalSyntheticLambda0;-><init>(Lcoil3/ComponentRegistry;)V
SPLcoil3/ComponentRegistry$$ExternalSyntheticLambda0;->invoke()Ljava/lang/Object;
Lcoil3/ComponentRegistry$$ExternalSyntheticLambda1;
SPLcoil3/ComponentRegistry$$ExternalSyntheticLambda1;-><init>(Lcoil3/ComponentRegistry;)V
SPLcoil3/ComponentRegistry$$ExternalSyntheticLambda1;->invoke()Ljava/lang/Object;
Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->$r8$lambda$IlEjb8YabdQqi6hUXx_kFA3X0vU(Lcoil3/decode/Decoder$Factory;)Ljava/util/List;
SPLcoil3/ComponentRegistry$Builder;->$r8$lambda$ZnQKRwR08HkwoV9px7i7gjuRF1c(Lcoil3/fetch/Fetcher$Factory;Lkotlin/reflect/KClass;)Ljava/util/List;
SPLcoil3/ComponentRegistry$Builder;-><init>(Lcoil3/ComponentRegistry;)V
SPLcoil3/ComponentRegistry$Builder;->add$lambda$11$lambda$10(Lcoil3/decode/Decoder$Factory;)Ljava/util/List;
SPLcoil3/ComponentRegistry$Builder;->add$lambda$8$lambda$7(Lcoil3/fetch/Fetcher$Factory;Lkotlin/reflect/KClass;)Ljava/util/List;
SPLcoil3/ComponentRegistry$Builder;->add(Lcoil3/decode/Decoder$Factory;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->add(Lcoil3/fetch/Fetcher$Factory;Lkotlin/reflect/KClass;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->add(Lcoil3/intercept/Interceptor;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->add(Lcoil3/key/Keyer;Lkotlin/reflect/KClass;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->add(Lcoil3/map/Mapper;Lkotlin/reflect/KClass;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->addDecoderFactories(Lkotlin/jvm/functions/Function0;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->addFetcherFactories(Lkotlin/jvm/functions/Function0;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/ComponentRegistry$Builder;->build()Lcoil3/ComponentRegistry;
Lcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda2;
SPLcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda2;-><init>(Lcoil3/fetch/Fetcher$Factory;Lkotlin/reflect/KClass;)V
SPLcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda2;->invoke()Ljava/lang/Object;
Lcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda3;
SPLcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda3;-><init>(Lcoil3/decode/Decoder$Factory;)V
SPLcoil3/ComponentRegistry$Builder$$ExternalSyntheticLambda3;->invoke()Ljava/lang/Object;
Lcoil3/EventListener;
SPLcoil3/EventListener;-><clinit>()V
SPLcoil3/EventListener;-><init>()V
SPLcoil3/EventListener;->decodeEnd(Lcoil3/request/ImageRequest;Lcoil3/decode/Decoder;Lcoil3/request/Options;Lcoil3/decode/DecodeResult;)V
SPLcoil3/EventListener;->decodeStart(Lcoil3/request/ImageRequest;Lcoil3/decode/Decoder;Lcoil3/request/Options;)V
SPLcoil3/EventListener;->fetchEnd(Lcoil3/request/ImageRequest;Lcoil3/fetch/Fetcher;Lcoil3/request/Options;Lcoil3/fetch/FetchResult;)V
SPLcoil3/EventListener;->fetchStart(Lcoil3/request/ImageRequest;Lcoil3/fetch/Fetcher;Lcoil3/request/Options;)V
SPLcoil3/EventListener;->keyEnd(Lcoil3/request/ImageRequest;Ljava/lang/String;)V
SPLcoil3/EventListener;->keyStart(Lcoil3/request/ImageRequest;Ljava/lang/Object;)V
SPLcoil3/EventListener;->mapEnd(Lcoil3/request/ImageRequest;Ljava/lang/Object;)V
SPLcoil3/EventListener;->mapStart(Lcoil3/request/ImageRequest;Ljava/lang/Object;)V
SPLcoil3/EventListener;->onStart(Lcoil3/request/ImageRequest;)V
SPLcoil3/EventListener;->onSuccess(Lcoil3/request/ImageRequest;Lcoil3/request/SuccessResult;)V
SPLcoil3/EventListener;->resolveSizeEnd(Lcoil3/request/ImageRequest;Lcoil3/size/Size;)V
SPLcoil3/EventListener;->resolveSizeStart(Lcoil3/request/ImageRequest;Lcoil3/size/SizeResolver;)V
Lcoil3/EventListener$Companion;
SPLcoil3/EventListener$Companion;-><init>()V
SPLcoil3/EventListener$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/EventListener$Companion$NONE$1;
SPLcoil3/EventListener$Companion$NONE$1;-><init>()V
Lcoil3/EventListener$Factory;
SPLcoil3/EventListener$Factory;-><clinit>()V
Lcoil3/EventListener$Factory$$ExternalSyntheticLambda0;
SPLcoil3/EventListener$Factory$$ExternalSyntheticLambda0;-><init>()V
SPLcoil3/EventListener$Factory$$ExternalSyntheticLambda0;->create(Lcoil3/request/ImageRequest;)Lcoil3/EventListener;
Lcoil3/EventListener$Factory$-CC;
SPLcoil3/EventListener$Factory$-CC;-><clinit>()V
SPLcoil3/EventListener$Factory$-CC;->NONE$lambda$0(Lcoil3/request/ImageRequest;)Lcoil3/EventListener;
Lcoil3/EventListener$Factory$Companion;
SPLcoil3/EventListener$Factory$Companion;-><clinit>()V
SPLcoil3/EventListener$Factory$Companion;-><init>()V
Lcoil3/Extras;
SPLcoil3/Extras;-><clinit>()V
SPLcoil3/Extras;-><init>(Ljava/util/Map;)V
SPLcoil3/Extras;-><init>(Ljava/util/Map;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/Extras;->access$getData$p(Lcoil3/Extras;)Ljava/util/Map;
SPLcoil3/Extras;->asMap()Ljava/util/Map;
SPLcoil3/Extras;->get(Lcoil3/Extras$Key;)Ljava/lang/Object;
SPLcoil3/Extras;->newBuilder()Lcoil3/Extras$Builder;
SPLcoil3/Extras;->toString()Ljava/lang/String;
Lcoil3/Extras$Builder;
SPLcoil3/Extras$Builder;-><init>()V
SPLcoil3/Extras$Builder;-><init>(Lcoil3/Extras;)V
SPLcoil3/Extras$Builder;-><init>(Ljava/util/Map;)V
SPLcoil3/Extras$Builder;->build()Lcoil3/Extras;
SPLcoil3/Extras$Builder;->set(Lcoil3/Extras$Key;Ljava/lang/Object;)Lcoil3/Extras$Builder;
SPLcoil3/Extras$Builder;->setAll(Lcoil3/Extras;)Lcoil3/Extras$Builder;
Lcoil3/Extras$Companion;
SPLcoil3/Extras$Companion;-><init>()V
SPLcoil3/Extras$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/Extras$Key;
SPLcoil3/Extras$Key;-><clinit>()V
SPLcoil3/Extras$Key;-><init>(Ljava/lang/Object;)V
SPLcoil3/Extras$Key;->getDefault()Ljava/lang/Object;
Lcoil3/Extras$Key$Companion;
SPLcoil3/Extras$Key$Companion;-><init>()V
SPLcoil3/Extras$Key$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/ExtrasKt;
SPLcoil3/ExtrasKt;->getExtra(Lcoil3/request/ImageRequest;Lcoil3/Extras$Key;)Ljava/lang/Object;
SPLcoil3/ExtrasKt;->getExtra(Lcoil3/request/Options;Lcoil3/Extras$Key;)Ljava/lang/Object;
SPLcoil3/ExtrasKt;->getOrDefault(Lcoil3/Extras;Lcoil3/Extras$Key;)Ljava/lang/Object;
Lcoil3/Image;
Lcoil3/ImageLoader;
Lcoil3/ImageLoader$Builder;
SPLcoil3/ImageLoader$Builder;-><init>(Landroid/content/Context;)V
SPLcoil3/ImageLoader$Builder;->build()Lcoil3/ImageLoader;
SPLcoil3/ImageLoader$Builder;->diskCache(Lkotlin/jvm/functions/Function0;)Lcoil3/ImageLoader$Builder;
SPLcoil3/ImageLoader$Builder;->getExtras()Lcoil3/Extras$Builder;
SPLcoil3/ImageLoader$Builder;->memoryCache(Lkotlin/jvm/functions/Function0;)Lcoil3/ImageLoader$Builder;
Lcoil3/ImageLoadersKt;
SPLcoil3/ImageLoadersKt;-><clinit>()V
SPLcoil3/ImageLoadersKt;->getServiceLoaderEnabled(Lcoil3/RealImageLoader$Options;)Z
Lcoil3/ImageLoaders_androidKt;
SPLcoil3/ImageLoaders_androidKt;-><clinit>()V
SPLcoil3/ImageLoaders_androidKt;->getBitmapFactoryExifOrientationStrategy(Lcoil3/RealImageLoader$Options;)Lcoil3/decode/ExifOrientationStrategy;
SPLcoil3/ImageLoaders_androidKt;->getBitmapFactoryMaxParallelism(Lcoil3/RealImageLoader$Options;)I
SPLcoil3/ImageLoaders_androidKt;->getImageDecoderEnabled(Lcoil3/RealImageLoader$Options;)Z
Lcoil3/Image_androidKt;
SPLcoil3/Image_androidKt;->asImage$default(Landroid/graphics/Bitmap;ZILjava/lang/Object;)Lcoil3/BitmapImage;
SPLcoil3/Image_androidKt;->asImage(Landroid/graphics/Bitmap;Z)Lcoil3/BitmapImage;
Lcoil3/RealImageLoader;
SPLcoil3/RealImageLoader;-><clinit>()V
SPLcoil3/RealImageLoader;-><init>(Lcoil3/RealImageLoader$Options;)V
SPLcoil3/RealImageLoader;->access$execute(Lcoil3/RealImageLoader;Lcoil3/request/ImageRequest;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLcoil3/RealImageLoader;->execute(Lcoil3/request/ImageRequest;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/RealImageLoader;->execute(Lcoil3/request/ImageRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/RealImageLoader;->getComponents()Lcoil3/ComponentRegistry;
SPLcoil3/RealImageLoader;->getDefaults()Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/RealImageLoader;->getDiskCache()Lcoil3/disk/DiskCache;
SPLcoil3/RealImageLoader;->getMemoryCache()Lcoil3/memory/MemoryCache;
SPLcoil3/RealImageLoader;->getOptions()Lcoil3/RealImageLoader$Options;
SPLcoil3/RealImageLoader;->onSuccess(Lcoil3/request/SuccessResult;Lcoil3/target/Target;Lcoil3/EventListener;)V
Lcoil3/RealImageLoader$Options;
SPLcoil3/RealImageLoader$Options;-><init>(Landroid/content/Context;Lcoil3/request/ImageRequest$Defaults;Lkotlin/Lazy;Lkotlin/Lazy;Lcoil3/EventListener$Factory;Lcoil3/ComponentRegistry;Lcoil3/util/Logger;)V
SPLcoil3/RealImageLoader$Options;->getApplication()Landroid/content/Context;
SPLcoil3/RealImageLoader$Options;->getComponentRegistry()Lcoil3/ComponentRegistry;
SPLcoil3/RealImageLoader$Options;->getDefaults()Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/RealImageLoader$Options;->getDiskCacheLazy()Lkotlin/Lazy;
SPLcoil3/RealImageLoader$Options;->getEventListenerFactory()Lcoil3/EventListener$Factory;
SPLcoil3/RealImageLoader$Options;->getLogger()Lcoil3/util/Logger;
SPLcoil3/RealImageLoader$Options;->getMemoryCacheLazy()Lkotlin/Lazy;
Lcoil3/RealImageLoader$execute$3;
SPLcoil3/RealImageLoader$execute$3;-><init>(Lcoil3/RealImageLoader;Lkotlin/coroutines/Continuation;)V
SPLcoil3/RealImageLoader$execute$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/RealImageLoader$execute$result$1;
SPLcoil3/RealImageLoader$execute$result$1;-><init>(Lcoil3/request/ImageRequest;Lcoil3/RealImageLoader;Lcoil3/size/Size;Lcoil3/EventListener;Lcoil3/Image;Lkotlin/coroutines/Continuation;)V
SPLcoil3/RealImageLoader$execute$result$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
SPLcoil3/RealImageLoader$execute$result$1;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
SPLcoil3/RealImageLoader$execute$result$1;->invoke(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/RealImageLoader$execute$result$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/RealImageLoaderKt;
SPLcoil3/RealImageLoaderKt;->$r8$lambda$TAnmP35-UjoHVTK3atGckt6lfN4()Ljava/util/List;
SPLcoil3/RealImageLoaderKt;->$r8$lambda$xGjjeCDAIKaBkbERqXqEUjIxN7A()Ljava/util/List;
SPLcoil3/RealImageLoaderKt;->CoroutineScope(Lcoil3/util/Logger;)Lkotlinx/coroutines/CoroutineScope;
SPLcoil3/RealImageLoaderKt;->access$CoroutineScope(Lcoil3/util/Logger;)Lkotlinx/coroutines/CoroutineScope;
SPLcoil3/RealImageLoaderKt;->addCommonComponents(Lcoil3/ComponentRegistry$Builder;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/RealImageLoaderKt;->addServiceLoaderComponents$lambda$3()Ljava/util/List;
SPLcoil3/RealImageLoaderKt;->addServiceLoaderComponents$lambda$6()Ljava/util/List;
SPLcoil3/RealImageLoaderKt;->addServiceLoaderComponents(Lcoil3/ComponentRegistry$Builder;Lcoil3/RealImageLoader$Options;)Lcoil3/ComponentRegistry$Builder;
Lcoil3/RealImageLoaderKt$$ExternalSyntheticLambda0;
SPLcoil3/RealImageLoaderKt$$ExternalSyntheticLambda0;-><init>()V
SPLcoil3/RealImageLoaderKt$$ExternalSyntheticLambda0;->invoke()Ljava/lang/Object;
Lcoil3/RealImageLoaderKt$$ExternalSyntheticLambda1;
SPLcoil3/RealImageLoaderKt$$ExternalSyntheticLambda1;-><init>()V
SPLcoil3/RealImageLoaderKt$$ExternalSyntheticLambda1;->invoke()Ljava/lang/Object;
Lcoil3/RealImageLoaderKt$CoroutineScope$$inlined$CoroutineExceptionHandler$1;
SPLcoil3/RealImageLoaderKt$CoroutineScope$$inlined$CoroutineExceptionHandler$1;-><init>(Lkotlinx/coroutines/CoroutineExceptionHandler$Key;Lcoil3/util/Logger;)V
Lcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$3$$inlined$sortedByDescending$1;
SPLcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$3$$inlined$sortedByDescending$1;-><init>()V
SPLcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$3$$inlined$sortedByDescending$1;->compare(Ljava/lang/Object;Ljava/lang/Object;)I
Lcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$6$$inlined$sortedByDescending$1;
SPLcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$6$$inlined$sortedByDescending$1;-><init>()V
SPLcoil3/RealImageLoaderKt$addServiceLoaderComponents$lambda$6$$inlined$sortedByDescending$1;->compare(Ljava/lang/Object;Ljava/lang/Object;)I
Lcoil3/RealImageLoader_androidKt;
SPLcoil3/RealImageLoader_androidKt;->addAndroidComponents(Lcoil3/ComponentRegistry$Builder;Lcoil3/RealImageLoader$Options;)Lcoil3/ComponentRegistry$Builder;
SPLcoil3/RealImageLoader_androidKt;->enableStaticImageDecoder(Lcoil3/RealImageLoader$Options;)Z
SPLcoil3/RealImageLoader_androidKt;->needsExecuteOnMainDispatcher(Lcoil3/request/ImageRequest;)Z
Lcoil3/RealImageLoader_jvmCommonKt;
SPLcoil3/RealImageLoader_jvmCommonKt;->addJvmComponents(Lcoil3/ComponentRegistry$Builder;Lcoil3/RealImageLoader$Options;)Lcoil3/ComponentRegistry$Builder;
Lcoil3/RealImageLoader_nonNativeKt;
SPLcoil3/RealImageLoader_nonNativeKt;->addAppleComponents(Lcoil3/ComponentRegistry$Builder;Lcoil3/RealImageLoader$Options;)Lcoil3/ComponentRegistry$Builder;
Lcoil3/SingletonImageLoader;
SPLcoil3/SingletonImageLoader;-><clinit>()V
SPLcoil3/SingletonImageLoader;-><init>()V
SPLcoil3/SingletonImageLoader;->get(Landroid/content/Context;)Lcoil3/ImageLoader;
SPLcoil3/SingletonImageLoader;->getReference()Ljava/util/concurrent/atomic/AtomicReference;
SPLcoil3/SingletonImageLoader;->newImageLoader(Landroid/content/Context;)Lcoil3/ImageLoader;
SPLcoil3/SingletonImageLoader;->setSafe(Lcoil3/SingletonImageLoader$Factory;)V
Lcoil3/SingletonImageLoader$Factory;
Lcoil3/Uri;
SPLcoil3/Uri;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
SPLcoil3/Uri;->getScheme()Ljava/lang/String;
SPLcoil3/Uri;->toString()Ljava/lang/String;
Lcoil3/UriKt;
SPLcoil3/UriKt;->getLength(Ljava/lang/String;)I
SPLcoil3/UriKt;->parseUri(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcoil3/Uri;
SPLcoil3/UriKt;->percentDecode(Ljava/lang/String;[B)Ljava/lang/String;
SPLcoil3/UriKt;->toUri$default(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lcoil3/Uri;
SPLcoil3/UriKt;->toUri(Ljava/lang/String;Ljava/lang/String;)Lcoil3/Uri;
Lcoil3/decode/BitmapFactoryDecoder$Factory;
SPLcoil3/decode/BitmapFactoryDecoder$Factory;-><init>(Lkotlinx/coroutines/sync/Semaphore;Lcoil3/decode/ExifOrientationStrategy;)V
Lcoil3/decode/DataSource;
SPLcoil3/decode/DataSource;->$values()[Lcoil3/decode/DataSource;
SPLcoil3/decode/DataSource;-><clinit>()V
SPLcoil3/decode/DataSource;-><init>(Ljava/lang/String;I)V
Lcoil3/decode/DecodeResult;
SPLcoil3/decode/DecodeResult;-><init>(Lcoil3/Image;Z)V
SPLcoil3/decode/DecodeResult;->getImage()Lcoil3/Image;
SPLcoil3/decode/DecodeResult;->isSampled()Z
Lcoil3/decode/DecodeUtils;
SPLcoil3/decode/DecodeUtils;-><clinit>()V
SPLcoil3/decode/DecodeUtils;-><init>()V
SPLcoil3/decode/DecodeUtils;->computeDstSize-sEdh43o(IILcoil3/size/Size;Lcoil3/size/Scale;Lcoil3/size/Size;)J
SPLcoil3/decode/DecodeUtils;->computeSizeMultiplier(IIIILcoil3/size/Scale;)D
SPLcoil3/decode/DecodeUtils;->toPx(Lcoil3/size/Dimension;Lcoil3/size/Scale;)I
Lcoil3/decode/DecodeUtils$WhenMappings;
SPLcoil3/decode/DecodeUtils$WhenMappings;-><clinit>()V
Lcoil3/decode/Decoder;
Lcoil3/decode/Decoder$Factory;
Lcoil3/decode/ExifOrientationStrategy;
SPLcoil3/decode/ExifOrientationStrategy;-><clinit>()V
Lcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda0;
SPLcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda0;-><init>()V
Lcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda1;
SPLcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda1;-><init>()V
Lcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda2;
SPLcoil3/decode/ExifOrientationStrategy$$ExternalSyntheticLambda2;-><init>()V
Lcoil3/decode/ExifOrientationStrategy$Companion;
SPLcoil3/decode/ExifOrientationStrategy$Companion;-><clinit>()V
SPLcoil3/decode/ExifOrientationStrategy$Companion;-><init>()V
Lcoil3/decode/FileImageSource;
SPLcoil3/decode/FileImageSource;-><init>(Lokio/Path;Lokio/FileSystem;Ljava/lang/String;Ljava/lang/AutoCloseable;Lcoil3/decode/ImageSource$Metadata;)V
SPLcoil3/decode/FileImageSource;->assertNotClosed()V
SPLcoil3/decode/FileImageSource;->close()V
SPLcoil3/decode/FileImageSource;->file()Lokio/Path;
SPLcoil3/decode/FileImageSource;->fileOrNull()Lokio/Path;
SPLcoil3/decode/FileImageSource;->getDiskCacheKey$coil_core_release()Ljava/lang/String;
SPLcoil3/decode/FileImageSource;->getFileSystem()Lokio/FileSystem;
SPLcoil3/decode/FileImageSource;->source()Lokio/BufferedSource;
Lcoil3/decode/ImageSource;
Lcoil3/decode/ImageSource$Metadata;
Lcoil3/decode/ImageSourceKt;
SPLcoil3/decode/ImageSourceKt;->ImageSource$default(Lokio/Path;Lokio/FileSystem;Ljava/lang/String;Ljava/lang/AutoCloseable;Lcoil3/decode/ImageSource$Metadata;ILjava/lang/Object;)Lcoil3/decode/ImageSource;
SPLcoil3/decode/ImageSourceKt;->ImageSource(Lokio/Path;Lokio/FileSystem;Ljava/lang/String;Ljava/lang/AutoCloseable;Lcoil3/decode/ImageSource$Metadata;)Lcoil3/decode/ImageSource;
Lcoil3/decode/StaticImageDecoder;
SPLcoil3/decode/StaticImageDecoder;-><init>(Landroid/graphics/ImageDecoder$Source;Ljava/lang/AutoCloseable;Lcoil3/request/Options;Lkotlinx/coroutines/sync/Semaphore;)V
SPLcoil3/decode/StaticImageDecoder;->access$configureImageDecoderProperties(Lcoil3/decode/StaticImageDecoder;Landroid/graphics/ImageDecoder;)V
SPLcoil3/decode/StaticImageDecoder;->access$getOptions$p(Lcoil3/decode/StaticImageDecoder;)Lcoil3/request/Options;
SPLcoil3/decode/StaticImageDecoder;->configureImageDecoderProperties(Landroid/graphics/ImageDecoder;)V
SPLcoil3/decode/StaticImageDecoder;->decode(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
Lcoil3/decode/StaticImageDecoder$$ExternalSyntheticLambda7;
SPLcoil3/decode/StaticImageDecoder$$ExternalSyntheticLambda7;-><init>()V
Lcoil3/decode/StaticImageDecoder$Factory;
SPLcoil3/decode/StaticImageDecoder$Factory;-><init>(Lkotlinx/coroutines/sync/Semaphore;)V
SPLcoil3/decode/StaticImageDecoder$Factory;->create(Lcoil3/fetch/SourceFetchResult;Lcoil3/request/Options;Lcoil3/ImageLoader;)Lcoil3/decode/Decoder;
SPLcoil3/decode/StaticImageDecoder$Factory;->isApplicable(Lcoil3/request/Options;)Z
Lcoil3/decode/StaticImageDecoder$decode$1;
SPLcoil3/decode/StaticImageDecoder$decode$1;-><init>(Lcoil3/decode/StaticImageDecoder;Lkotlin/coroutines/Continuation;)V
SPLcoil3/decode/StaticImageDecoder$decode$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/decode/StaticImageDecoder$decode$lambda$2$lambda$1$$inlined$decodeBitmap$1;
SPLcoil3/decode/StaticImageDecoder$decode$lambda$2$lambda$1$$inlined$decodeBitmap$1;-><init>(Lcoil3/decode/StaticImageDecoder;Lkotlin/jvm/internal/Ref$BooleanRef;)V
SPLcoil3/decode/StaticImageDecoder$decode$lambda$2$lambda$1$$inlined$decodeBitmap$1;->onHeaderDecoded(Landroid/graphics/ImageDecoder;Landroid/graphics/ImageDecoder$ImageInfo;Landroid/graphics/ImageDecoder$Source;)V
Lcoil3/decode/StaticImageDecoderKt;
SPLcoil3/decode/StaticImageDecoderKt;->toImageDecoderSourceOrNull(Lcoil3/decode/ImageSource;Lcoil3/request/Options;Z)Landroid/graphics/ImageDecoder$Source;
Lcoil3/disk/DiskCache;
Lcoil3/disk/DiskCache$Builder;
SPLcoil3/disk/DiskCache$Builder;-><init>()V
SPLcoil3/disk/DiskCache$Builder;->build()Lcoil3/disk/DiskCache;
SPLcoil3/disk/DiskCache$Builder;->directory(Lokio/Path;)Lcoil3/disk/DiskCache$Builder;
SPLcoil3/disk/DiskCache$Builder;->maxSizeBytes(J)Lcoil3/disk/DiskCache$Builder;
Lcoil3/disk/DiskCache$Editor;
Lcoil3/disk/DiskCache$Snapshot;
Lcoil3/disk/DiskLruCache;
SPLcoil3/disk/DiskLruCache;-><clinit>()V
SPLcoil3/disk/DiskLruCache;-><init>(Lokio/FileSystem;Lokio/Path;Lkotlin/coroutines/CoroutineContext;JII)V
SPLcoil3/disk/DiskLruCache;->access$completeEdit(Lcoil3/disk/DiskLruCache;Lcoil3/disk/DiskLruCache$Editor;Z)V
SPLcoil3/disk/DiskLruCache;->access$getDirectory$p(Lcoil3/disk/DiskLruCache;)Lokio/Path;
SPLcoil3/disk/DiskLruCache;->access$getFileSystem$p(Lcoil3/disk/DiskLruCache;)Lcoil3/disk/DiskLruCache$fileSystem$1;
SPLcoil3/disk/DiskLruCache;->access$getLock$p(Lcoil3/disk/DiskLruCache;)Ljava/lang/Object;
SPLcoil3/disk/DiskLruCache;->access$getValueCount$p(Lcoil3/disk/DiskLruCache;)I
SPLcoil3/disk/DiskLruCache;->checkNotClosed()V
SPLcoil3/disk/DiskLruCache;->completeEdit(Lcoil3/disk/DiskLruCache$Editor;Z)V
SPLcoil3/disk/DiskLruCache;->edit(Ljava/lang/String;)Lcoil3/disk/DiskLruCache$Editor;
SPLcoil3/disk/DiskLruCache;->get(Ljava/lang/String;)Lcoil3/disk/DiskLruCache$Snapshot;
SPLcoil3/disk/DiskLruCache;->initialize()V
SPLcoil3/disk/DiskLruCache;->journalRewriteRequired()Z
SPLcoil3/disk/DiskLruCache;->newJournalWriter()Lokio/BufferedSink;
SPLcoil3/disk/DiskLruCache;->processJournal()V
SPLcoil3/disk/DiskLruCache;->readJournal()V
SPLcoil3/disk/DiskLruCache;->readJournalLine(Ljava/lang/String;)V
SPLcoil3/disk/DiskLruCache;->validateKey(Ljava/lang/String;)V
SPLcoil3/disk/DiskLruCache;->writeJournal()V
Lcoil3/disk/DiskLruCache$$ExternalSyntheticLambda0;
SPLcoil3/disk/DiskLruCache$$ExternalSyntheticLambda0;-><init>(Lcoil3/disk/DiskLruCache;)V
Lcoil3/disk/DiskLruCache$Companion;
SPLcoil3/disk/DiskLruCache$Companion;-><init>()V
SPLcoil3/disk/DiskLruCache$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/disk/DiskLruCache$Editor;
SPLcoil3/disk/DiskLruCache$Editor;-><init>(Lcoil3/disk/DiskLruCache;Lcoil3/disk/DiskLruCache$Entry;)V
SPLcoil3/disk/DiskLruCache$Editor;->commit()V
SPLcoil3/disk/DiskLruCache$Editor;->commitAndGet()Lcoil3/disk/DiskLruCache$Snapshot;
SPLcoil3/disk/DiskLruCache$Editor;->complete(Z)V
SPLcoil3/disk/DiskLruCache$Editor;->file(I)Lokio/Path;
SPLcoil3/disk/DiskLruCache$Editor;->getEntry()Lcoil3/disk/DiskLruCache$Entry;
SPLcoil3/disk/DiskLruCache$Editor;->getWritten()[Z
Lcoil3/disk/DiskLruCache$Entry;
SPLcoil3/disk/DiskLruCache$Entry;-><init>(Lcoil3/disk/DiskLruCache;Ljava/lang/String;)V
SPLcoil3/disk/DiskLruCache$Entry;->getCleanFiles()Ljava/util/ArrayList;
SPLcoil3/disk/DiskLruCache$Entry;->getCurrentEditor()Lcoil3/disk/DiskLruCache$Editor;
SPLcoil3/disk/DiskLruCache$Entry;->getDirtyFiles()Ljava/util/ArrayList;
SPLcoil3/disk/DiskLruCache$Entry;->getKey()Ljava/lang/String;
SPLcoil3/disk/DiskLruCache$Entry;->getLengths()[J
SPLcoil3/disk/DiskLruCache$Entry;->getLockingSnapshotCount()I
SPLcoil3/disk/DiskLruCache$Entry;->getZombie()Z
SPLcoil3/disk/DiskLruCache$Entry;->setCurrentEditor(Lcoil3/disk/DiskLruCache$Editor;)V
SPLcoil3/disk/DiskLruCache$Entry;->setLengths(Ljava/util/List;)V
SPLcoil3/disk/DiskLruCache$Entry;->setLockingSnapshotCount(I)V
SPLcoil3/disk/DiskLruCache$Entry;->setReadable(Z)V
SPLcoil3/disk/DiskLruCache$Entry;->snapshot()Lcoil3/disk/DiskLruCache$Snapshot;
SPLcoil3/disk/DiskLruCache$Entry;->writeLengths(Lokio/BufferedSink;)V
Lcoil3/disk/DiskLruCache$Snapshot;
SPLcoil3/disk/DiskLruCache$Snapshot;-><init>(Lcoil3/disk/DiskLruCache;Lcoil3/disk/DiskLruCache$Entry;)V
SPLcoil3/disk/DiskLruCache$Snapshot;->close()V
SPLcoil3/disk/DiskLruCache$Snapshot;->file(I)Lokio/Path;
Lcoil3/disk/DiskLruCache$fileSystem$1;
SPLcoil3/disk/DiskLruCache$fileSystem$1;-><init>(Lokio/FileSystem;)V
SPLcoil3/disk/DiskLruCache$fileSystem$1;->sink(Lokio/Path;Z)Lokio/Sink;
Lcoil3/disk/FaultHidingSink;
SPLcoil3/disk/FaultHidingSink;-><init>(Lokio/Sink;Lkotlin/jvm/functions/Function1;)V
SPLcoil3/disk/FaultHidingSink;->flush()V
SPLcoil3/disk/FaultHidingSink;->write(Lokio/Buffer;J)V
Lcoil3/disk/RealDiskCache;
SPLcoil3/disk/RealDiskCache;-><clinit>()V
SPLcoil3/disk/RealDiskCache;-><init>(JLokio/Path;Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;)V
SPLcoil3/disk/RealDiskCache;->getDirectory()Lokio/Path;
SPLcoil3/disk/RealDiskCache;->getFileSystem()Lokio/FileSystem;
SPLcoil3/disk/RealDiskCache;->getMaxSize()J
SPLcoil3/disk/RealDiskCache;->hash(Ljava/lang/String;)Ljava/lang/String;
SPLcoil3/disk/RealDiskCache;->openEditor(Ljava/lang/String;)Lcoil3/disk/DiskCache$Editor;
SPLcoil3/disk/RealDiskCache;->openSnapshot(Ljava/lang/String;)Lcoil3/disk/DiskCache$Snapshot;
Lcoil3/disk/RealDiskCache$Companion;
SPLcoil3/disk/RealDiskCache$Companion;-><init>()V
SPLcoil3/disk/RealDiskCache$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/disk/RealDiskCache$RealEditor;
SPLcoil3/disk/RealDiskCache$RealEditor;-><init>(Lcoil3/disk/DiskLruCache$Editor;)V
SPLcoil3/disk/RealDiskCache$RealEditor;->commitAndOpenSnapshot()Lcoil3/disk/DiskCache$Snapshot;
SPLcoil3/disk/RealDiskCache$RealEditor;->commitAndOpenSnapshot()Lcoil3/disk/RealDiskCache$RealSnapshot;
SPLcoil3/disk/RealDiskCache$RealEditor;->getData()Lokio/Path;
SPLcoil3/disk/RealDiskCache$RealEditor;->getMetadata()Lokio/Path;
Lcoil3/disk/RealDiskCache$RealSnapshot;
SPLcoil3/disk/RealDiskCache$RealSnapshot;-><init>(Lcoil3/disk/DiskLruCache$Snapshot;)V
SPLcoil3/disk/RealDiskCache$RealSnapshot;->close()V
SPLcoil3/disk/RealDiskCache$RealSnapshot;->getData()Lokio/Path;
SPLcoil3/disk/RealDiskCache$RealSnapshot;->getMetadata()Lokio/Path;
Lcoil3/fetch/AssetUriFetcher$Factory;
SPLcoil3/fetch/AssetUriFetcher$Factory;-><init>()V
Lcoil3/fetch/BitmapFetcher$Factory;
SPLcoil3/fetch/BitmapFetcher$Factory;-><init>()V
Lcoil3/fetch/ByteArrayFetcher$Factory;
SPLcoil3/fetch/ByteArrayFetcher$Factory;-><init>()V
Lcoil3/fetch/ByteBufferFetcher$Factory;
SPLcoil3/fetch/ByteBufferFetcher$Factory;-><init>()V
Lcoil3/fetch/ContentUriFetcher$Factory;
SPLcoil3/fetch/ContentUriFetcher$Factory;-><init>()V
Lcoil3/fetch/DataUriFetcher$Factory;
SPLcoil3/fetch/DataUriFetcher$Factory;-><init>()V
Lcoil3/fetch/DrawableFetcher$Factory;
SPLcoil3/fetch/DrawableFetcher$Factory;-><init>()V
Lcoil3/fetch/FetchResult;
Lcoil3/fetch/Fetcher;
Lcoil3/fetch/Fetcher$Factory;
Lcoil3/fetch/FileUriFetcher$Factory;
SPLcoil3/fetch/FileUriFetcher$Factory;-><init>()V
Lcoil3/fetch/ImageFetchResult;
Lcoil3/fetch/JarFileFetcher$Factory;
SPLcoil3/fetch/JarFileFetcher$Factory;-><init>()V
Lcoil3/fetch/ResourceUriFetcher$Factory;
SPLcoil3/fetch/ResourceUriFetcher$Factory;-><init>()V
Lcoil3/fetch/SourceFetchResult;
SPLcoil3/fetch/SourceFetchResult;-><init>(Lcoil3/decode/ImageSource;Ljava/lang/String;Lcoil3/decode/DataSource;)V
SPLcoil3/fetch/SourceFetchResult;->getDataSource()Lcoil3/decode/DataSource;
SPLcoil3/fetch/SourceFetchResult;->getMimeType()Ljava/lang/String;
SPLcoil3/fetch/SourceFetchResult;->getSource()Lcoil3/decode/ImageSource;
Lcoil3/intercept/EngineInterceptor;
SPLcoil3/intercept/EngineInterceptor;-><clinit>()V
SPLcoil3/intercept/EngineInterceptor;-><init>(Lcoil3/ImageLoader;Lcoil3/util/SystemCallbacks;Lcoil3/request/RequestService;Lcoil3/util/Logger;)V
SPLcoil3/intercept/EngineInterceptor;->access$decode(Lcoil3/intercept/EngineInterceptor;Lcoil3/fetch/SourceFetchResult;Lcoil3/ComponentRegistry;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor;->access$execute(Lcoil3/intercept/EngineInterceptor;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor;->access$fetch(Lcoil3/intercept/EngineInterceptor;Lcoil3/ComponentRegistry;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor;->access$getMemoryCacheService$p(Lcoil3/intercept/EngineInterceptor;)Lcoil3/memory/MemoryCacheService;
SPLcoil3/intercept/EngineInterceptor;->access$getSystemCallbacks$p(Lcoil3/intercept/EngineInterceptor;)Lcoil3/util/SystemCallbacks;
SPLcoil3/intercept/EngineInterceptor;->decode(Lcoil3/fetch/SourceFetchResult;Lcoil3/ComponentRegistry;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLcoil3/intercept/EngineInterceptor;->execute(Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor;->fetch(Lcoil3/ComponentRegistry;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor;->intercept(Lcoil3/intercept/Interceptor$Chain;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$Companion;
SPLcoil3/intercept/EngineInterceptor$Companion;-><init>()V
SPLcoil3/intercept/EngineInterceptor$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/intercept/EngineInterceptor$ExecuteResult;
SPLcoil3/intercept/EngineInterceptor$ExecuteResult;-><init>(Lcoil3/Image;ZLcoil3/decode/DataSource;Ljava/lang/String;)V
SPLcoil3/intercept/EngineInterceptor$ExecuteResult;->getDataSource()Lcoil3/decode/DataSource;
SPLcoil3/intercept/EngineInterceptor$ExecuteResult;->getDiskCacheKey()Ljava/lang/String;
SPLcoil3/intercept/EngineInterceptor$ExecuteResult;->getImage()Lcoil3/Image;
SPLcoil3/intercept/EngineInterceptor$ExecuteResult;->isSampled()Z
Lcoil3/intercept/EngineInterceptor$decode$1;
SPLcoil3/intercept/EngineInterceptor$decode$1;-><init>(Lcoil3/intercept/EngineInterceptor;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$decode$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$execute$1;
SPLcoil3/intercept/EngineInterceptor$execute$1;-><init>(Lcoil3/intercept/EngineInterceptor;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$execute$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$execute$executeResult$1;
SPLcoil3/intercept/EngineInterceptor$execute$executeResult$1;-><init>(Lcoil3/intercept/EngineInterceptor;Lkotlin/jvm/internal/Ref$ObjectRef;Lkotlin/jvm/internal/Ref$ObjectRef;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lkotlin/jvm/internal/Ref$ObjectRef;Lcoil3/EventListener;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$execute$executeResult$1;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
SPLcoil3/intercept/EngineInterceptor$execute$executeResult$1;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor$execute$executeResult$1;->invoke(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
SPLcoil3/intercept/EngineInterceptor$execute$executeResult$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$fetch$1;
SPLcoil3/intercept/EngineInterceptor$fetch$1;-><init>(Lcoil3/intercept/EngineInterceptor;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$fetch$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$intercept$1;
SPLcoil3/intercept/EngineInterceptor$intercept$1;-><init>(Lcoil3/intercept/EngineInterceptor;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$intercept$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptor$intercept$2;
SPLcoil3/intercept/EngineInterceptor$intercept$2;-><init>(Lcoil3/intercept/EngineInterceptor;Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;Lcoil3/memory/MemoryCache$Key;Lcoil3/intercept/Interceptor$Chain;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/EngineInterceptor$intercept$2;->create(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
SPLcoil3/intercept/EngineInterceptor$intercept$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptorKt;
SPLcoil3/intercept/EngineInterceptorKt;->transform(Lcoil3/intercept/EngineInterceptor$ExecuteResult;Lcoil3/request/ImageRequest;Lcoil3/request/Options;Lcoil3/EventListener;Lcoil3/util/Logger;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
Lcoil3/intercept/EngineInterceptorKt$transform$1;
SPLcoil3/intercept/EngineInterceptorKt$transform$1;-><init>(Lkotlin/coroutines/Continuation;)V
Lcoil3/intercept/Interceptor;
Lcoil3/intercept/Interceptor$Chain;
Lcoil3/intercept/RealInterceptorChain;
SPLcoil3/intercept/RealInterceptorChain;-><init>(Lcoil3/request/ImageRequest;Ljava/util/List;ILcoil3/request/ImageRequest;Lcoil3/size/Size;Lcoil3/EventListener;Z)V
SPLcoil3/intercept/RealInterceptorChain;->checkRequest(Lcoil3/request/ImageRequest;Lcoil3/intercept/Interceptor;)V
SPLcoil3/intercept/RealInterceptorChain;->copy$default(Lcoil3/intercept/RealInterceptorChain;ILcoil3/request/ImageRequest;Lcoil3/size/Size;ILjava/lang/Object;)Lcoil3/intercept/RealInterceptorChain;
SPLcoil3/intercept/RealInterceptorChain;->copy(ILcoil3/request/ImageRequest;Lcoil3/size/Size;)Lcoil3/intercept/RealInterceptorChain;
SPLcoil3/intercept/RealInterceptorChain;->getEventListener()Lcoil3/EventListener;
SPLcoil3/intercept/RealInterceptorChain;->getRequest()Lcoil3/request/ImageRequest;
SPLcoil3/intercept/RealInterceptorChain;->getSize()Lcoil3/size/Size;
SPLcoil3/intercept/RealInterceptorChain;->isPlaceholderCached()Z
SPLcoil3/intercept/RealInterceptorChain;->proceed(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
Lcoil3/intercept/RealInterceptorChain$proceed$1;
SPLcoil3/intercept/RealInterceptorChain$proceed$1;-><init>(Lcoil3/intercept/RealInterceptorChain;Lkotlin/coroutines/Continuation;)V
SPLcoil3/intercept/RealInterceptorChain$proceed$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/key/AndroidResourceUriKeyer;
SPLcoil3/key/AndroidResourceUriKeyer;-><init>()V
SPLcoil3/key/AndroidResourceUriKeyer;->key(Lcoil3/Uri;Lcoil3/request/Options;)Ljava/lang/String;
SPLcoil3/key/AndroidResourceUriKeyer;->key(Ljava/lang/Object;Lcoil3/request/Options;)Ljava/lang/String;
Lcoil3/key/FileUriKeyer;
SPLcoil3/key/FileUriKeyer;-><init>()V
SPLcoil3/key/FileUriKeyer;->key(Lcoil3/Uri;Lcoil3/request/Options;)Ljava/lang/String;
SPLcoil3/key/FileUriKeyer;->key(Ljava/lang/Object;Lcoil3/request/Options;)Ljava/lang/String;
Lcoil3/key/Keyer;
Lcoil3/key/UriKeyer;
SPLcoil3/key/UriKeyer;-><init>()V
SPLcoil3/key/UriKeyer;->key(Lcoil3/Uri;Lcoil3/request/Options;)Ljava/lang/String;
SPLcoil3/key/UriKeyer;->key(Ljava/lang/Object;Lcoil3/request/Options;)Ljava/lang/String;
Lcoil3/map/AndroidUriMapper;
SPLcoil3/map/AndroidUriMapper;-><init>()V
Lcoil3/map/FileMapper;
SPLcoil3/map/FileMapper;-><init>()V
Lcoil3/map/Mapper;
Lcoil3/map/PathMapper;
SPLcoil3/map/PathMapper;-><init>()V
Lcoil3/map/ResourceIntMapper;
SPLcoil3/map/ResourceIntMapper;-><init>()V
Lcoil3/map/StringMapper;
SPLcoil3/map/StringMapper;-><init>()V
SPLcoil3/map/StringMapper;->map(Ljava/lang/Object;Lcoil3/request/Options;)Ljava/lang/Object;
SPLcoil3/map/StringMapper;->map(Ljava/lang/String;Lcoil3/request/Options;)Lcoil3/Uri;
Lcoil3/memory/MemoryCache;
Lcoil3/memory/MemoryCache$Builder;
SPLcoil3/memory/MemoryCache$Builder;->$r8$lambda$-yj6xnvcPNZv0zjOrvC3Vb-NwAE(DLandroid/content/Context;)J
SPLcoil3/memory/MemoryCache$Builder;-><init>()V
SPLcoil3/memory/MemoryCache$Builder;->build()Lcoil3/memory/MemoryCache;
SPLcoil3/memory/MemoryCache$Builder;->maxSizePercent$lambda$5$lambda$4(DLandroid/content/Context;)J
SPLcoil3/memory/MemoryCache$Builder;->maxSizePercent(Landroid/content/Context;D)Lcoil3/memory/MemoryCache$Builder;
Lcoil3/memory/MemoryCache$Builder$$ExternalSyntheticLambda0;
SPLcoil3/memory/MemoryCache$Builder$$ExternalSyntheticLambda0;-><init>(DLandroid/content/Context;)V
SPLcoil3/memory/MemoryCache$Builder$$ExternalSyntheticLambda0;->invoke()Ljava/lang/Object;
Lcoil3/memory/MemoryCache$Key;
SPLcoil3/memory/MemoryCache$Key;-><init>(Ljava/lang/String;Ljava/util/Map;)V
SPLcoil3/memory/MemoryCache$Key;->hashCode()I
Lcoil3/memory/MemoryCache$Value;
SPLcoil3/memory/MemoryCache$Value;-><init>(Lcoil3/Image;Ljava/util/Map;)V
SPLcoil3/memory/MemoryCache$Value;->getExtras()Ljava/util/Map;
SPLcoil3/memory/MemoryCache$Value;->getImage()Lcoil3/Image;
Lcoil3/memory/MemoryCacheService;
SPLcoil3/memory/MemoryCacheService;-><clinit>()V
SPLcoil3/memory/MemoryCacheService;-><init>(Lcoil3/ImageLoader;Lcoil3/request/RequestService;Lcoil3/util/Logger;)V
SPLcoil3/memory/MemoryCacheService;->getCacheValue(Lcoil3/request/ImageRequest;Lcoil3/memory/MemoryCache$Key;Lcoil3/size/Size;Lcoil3/size/Scale;)Lcoil3/memory/MemoryCache$Value;
SPLcoil3/memory/MemoryCacheService;->newCacheKey(Lcoil3/request/ImageRequest;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/EventListener;)Lcoil3/memory/MemoryCache$Key;
SPLcoil3/memory/MemoryCacheService;->setCacheValue(Lcoil3/memory/MemoryCache$Key;Lcoil3/request/ImageRequest;Lcoil3/intercept/EngineInterceptor$ExecuteResult;)Z
Lcoil3/memory/MemoryCacheService$Companion;
SPLcoil3/memory/MemoryCacheService$Companion;-><init>()V
SPLcoil3/memory/MemoryCacheService$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/memory/RealMemoryCache;
SPLcoil3/memory/RealMemoryCache;-><init>(Lcoil3/memory/StrongMemoryCache;Lcoil3/memory/WeakMemoryCache;)V
SPLcoil3/memory/RealMemoryCache;->get(Lcoil3/memory/MemoryCache$Key;)Lcoil3/memory/MemoryCache$Value;
SPLcoil3/memory/RealMemoryCache;->set(Lcoil3/memory/MemoryCache$Key;Lcoil3/memory/MemoryCache$Value;)V
Lcoil3/memory/RealStrongMemoryCache;
SPLcoil3/memory/RealStrongMemoryCache;-><init>(JLcoil3/memory/WeakMemoryCache;)V
SPLcoil3/memory/RealStrongMemoryCache;->get(Lcoil3/memory/MemoryCache$Key;)Lcoil3/memory/MemoryCache$Value;
SPLcoil3/memory/RealStrongMemoryCache;->getMaxSize()J
SPLcoil3/memory/RealStrongMemoryCache;->set(Lcoil3/memory/MemoryCache$Key;Lcoil3/Image;Ljava/util/Map;J)V
Lcoil3/memory/RealStrongMemoryCache$InternalValue;
SPLcoil3/memory/RealStrongMemoryCache$InternalValue;-><init>(Lcoil3/Image;Ljava/util/Map;J)V
SPLcoil3/memory/RealStrongMemoryCache$InternalValue;->getSize()J
Lcoil3/memory/RealStrongMemoryCache$cache$1;
SPLcoil3/memory/RealStrongMemoryCache$cache$1;-><init>(JLcoil3/memory/RealStrongMemoryCache;)V
SPLcoil3/memory/RealStrongMemoryCache$cache$1;->sizeOf(Lcoil3/memory/MemoryCache$Key;Lcoil3/memory/RealStrongMemoryCache$InternalValue;)J
SPLcoil3/memory/RealStrongMemoryCache$cache$1;->sizeOf(Ljava/lang/Object;Ljava/lang/Object;)J
Lcoil3/memory/RealWeakMemoryCache;
SPLcoil3/memory/RealWeakMemoryCache;-><clinit>()V
SPLcoil3/memory/RealWeakMemoryCache;-><init>()V
SPLcoil3/memory/RealWeakMemoryCache;->get(Lcoil3/memory/MemoryCache$Key;)Lcoil3/memory/MemoryCache$Value;
Lcoil3/memory/RealWeakMemoryCache$Companion;
SPLcoil3/memory/RealWeakMemoryCache$Companion;-><init>()V
SPLcoil3/memory/RealWeakMemoryCache$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/memory/StrongMemoryCache;
Lcoil3/memory/WeakMemoryCache;
Lcoil3/request/AndroidRequestService;
SPLcoil3/request/AndroidRequestService;-><init>(Lcoil3/ImageLoader;Lcoil3/util/SystemCallbacks;Lcoil3/util/Logger;)V
SPLcoil3/request/AndroidRequestService;->isBitmapConfigValidMainThread(Lcoil3/request/ImageRequest;Lcoil3/size/Size;)Z
SPLcoil3/request/AndroidRequestService;->isBitmapConfigValidWorkerThread(Lcoil3/request/Options;)Z
SPLcoil3/request/AndroidRequestService;->isConfigValidForHardware(Lcoil3/request/ImageRequest;Landroid/graphics/Bitmap$Config;)Z
SPLcoil3/request/AndroidRequestService;->options(Lcoil3/request/ImageRequest;Lcoil3/size/Size;)Lcoil3/request/Options;
SPLcoil3/request/AndroidRequestService;->requestDelegate(Lcoil3/request/ImageRequest;Lkotlinx/coroutines/Job;Z)Lcoil3/request/RequestDelegate;
SPLcoil3/request/AndroidRequestService;->resolveExtras(Lcoil3/request/ImageRequest;Lcoil3/size/Size;)Lcoil3/Extras;
SPLcoil3/request/AndroidRequestService;->updateOptions(Lcoil3/request/Options;)Lcoil3/request/Options;
SPLcoil3/request/AndroidRequestService;->updateRequest(Lcoil3/request/ImageRequest;)Lcoil3/request/ImageRequest;
Lcoil3/request/BaseRequestDelegate;
SPLcoil3/request/BaseRequestDelegate;-><init>(Lkotlinx/coroutines/Job;)V
SPLcoil3/request/BaseRequestDelegate;->assertActive()V
SPLcoil3/request/BaseRequestDelegate;->box-impl(Lkotlinx/coroutines/Job;)Lcoil3/request/BaseRequestDelegate;
SPLcoil3/request/BaseRequestDelegate;->complete()V
SPLcoil3/request/BaseRequestDelegate;->constructor-impl(Lkotlinx/coroutines/Job;)Lkotlinx/coroutines/Job;
SPLcoil3/request/BaseRequestDelegate;->start()V
Lcoil3/request/CachePolicy;
SPLcoil3/request/CachePolicy;->$values()[Lcoil3/request/CachePolicy;
SPLcoil3/request/CachePolicy;-><clinit>()V
SPLcoil3/request/CachePolicy;-><init>(Ljava/lang/String;IZZ)V
SPLcoil3/request/CachePolicy;->getReadEnabled()Z
SPLcoil3/request/CachePolicy;->getWriteEnabled()Z
Lcoil3/request/ErrorResult;
Lcoil3/request/ImageRequest;
HSPLcoil3/request/ImageRequest;-><init>(Landroid/content/Context;Ljava/lang/Object;Lcoil3/target/Target;Lcoil3/request/ImageRequest$Listener;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Lokio/FileSystem;Lkotlin/Pair;Lcoil3/decode/Decoder$Factory;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/memory/MemoryCache$Key;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;Lcoil3/request/ImageRequest$Defined;Lcoil3/request/ImageRequest$Defaults;)V
SPLcoil3/request/ImageRequest;-><init>(Landroid/content/Context;Ljava/lang/Object;Lcoil3/target/Target;Lcoil3/request/ImageRequest$Listener;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Lokio/FileSystem;Lkotlin/Pair;Lcoil3/decode/Decoder$Factory;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/memory/MemoryCache$Key;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;Lcoil3/request/ImageRequest$Defined;Lcoil3/request/ImageRequest$Defaults;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/request/ImageRequest;->equals(Ljava/lang/Object;)Z
SPLcoil3/request/ImageRequest;->getContext()Landroid/content/Context;
SPLcoil3/request/ImageRequest;->getData()Ljava/lang/Object;
SPLcoil3/request/ImageRequest;->getDecoderCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest;->getDecoderFactory()Lcoil3/decode/Decoder$Factory;
SPLcoil3/request/ImageRequest;->getDefaults()Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/request/ImageRequest;->getDefined()Lcoil3/request/ImageRequest$Defined;
SPLcoil3/request/ImageRequest;->getDiskCacheKey()Ljava/lang/String;
SPLcoil3/request/ImageRequest;->getDiskCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest;->getExtras()Lcoil3/Extras;
SPLcoil3/request/ImageRequest;->getFetcherCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest;->getFetcherFactory()Lkotlin/Pair;
SPLcoil3/request/ImageRequest;->getFileSystem()Lokio/FileSystem;
SPLcoil3/request/ImageRequest;->getInterceptorCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest;->getListener()Lcoil3/request/ImageRequest$Listener;
SPLcoil3/request/ImageRequest;->getMemoryCacheKey()Ljava/lang/String;
SPLcoil3/request/ImageRequest;->getMemoryCacheKeyExtras()Ljava/util/Map;
SPLcoil3/request/ImageRequest;->getMemoryCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest;->getNetworkCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest;->getPlaceholderMemoryCacheKey()Lcoil3/memory/MemoryCache$Key;
SPLcoil3/request/ImageRequest;->getPrecision()Lcoil3/size/Precision;
SPLcoil3/request/ImageRequest;->getScale()Lcoil3/size/Scale;
SPLcoil3/request/ImageRequest;->getSizeResolver()Lcoil3/size/SizeResolver;
SPLcoil3/request/ImageRequest;->getTarget()Lcoil3/target/Target;
SPLcoil3/request/ImageRequest;->newBuilder$default(Lcoil3/request/ImageRequest;Landroid/content/Context;ILjava/lang/Object;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest;->newBuilder(Landroid/content/Context;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest;->placeholder()Lcoil3/Image;
Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;-><init>(Landroid/content/Context;)V
HSPLcoil3/request/ImageRequest$Builder;-><init>(Lcoil3/request/ImageRequest;Landroid/content/Context;)V
HSPLcoil3/request/ImageRequest$Builder;->build()Lcoil3/request/ImageRequest;
SPLcoil3/request/ImageRequest$Builder;->data(Ljava/lang/Object;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;->defaults(Lcoil3/request/ImageRequest$Defaults;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;->getExtras()Lcoil3/Extras$Builder;
SPLcoil3/request/ImageRequest$Builder;->precision(Lcoil3/size/Precision;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;->scale(Lcoil3/size/Scale;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;->size(Lcoil3/size/SizeResolver;)Lcoil3/request/ImageRequest$Builder;
SPLcoil3/request/ImageRequest$Builder;->target(Lcoil3/target/Target;)Lcoil3/request/ImageRequest$Builder;
Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/request/ImageRequest$Defaults;-><clinit>()V
SPLcoil3/request/ImageRequest$Defaults;-><init>(Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;)V
SPLcoil3/request/ImageRequest$Defaults;-><init>(Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/request/ImageRequest$Defaults;->copy$default(Lcoil3/request/ImageRequest$Defaults;Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;ILjava/lang/Object;)Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/request/ImageRequest$Defaults;->copy(Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;Lcoil3/Extras;)Lcoil3/request/ImageRequest$Defaults;
SPLcoil3/request/ImageRequest$Defaults;->getDecoderCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defaults;->getDiskCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defaults;->getExtras()Lcoil3/Extras;
SPLcoil3/request/ImageRequest$Defaults;->getFetcherCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defaults;->getFileSystem()Lokio/FileSystem;
SPLcoil3/request/ImageRequest$Defaults;->getInterceptorCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defaults;->getMemoryCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defaults;->getNetworkCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defaults;->getPlaceholderFactory()Lkotlin/jvm/functions/Function1;
SPLcoil3/request/ImageRequest$Defaults;->getPrecision()Lcoil3/size/Precision;
SPLcoil3/request/ImageRequest$Defaults;->getScale()Lcoil3/size/Scale;
SPLcoil3/request/ImageRequest$Defaults;->getSizeResolver()Lcoil3/size/SizeResolver;
Lcoil3/request/ImageRequest$Defaults$Companion;
SPLcoil3/request/ImageRequest$Defaults$Companion;-><init>()V
SPLcoil3/request/ImageRequest$Defaults$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/request/ImageRequest$Defined;
SPLcoil3/request/ImageRequest$Defined;-><init>(Lokio/FileSystem;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/CoroutineContext;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lcoil3/size/SizeResolver;Lcoil3/size/Scale;Lcoil3/size/Precision;)V
SPLcoil3/request/ImageRequest$Defined;->getDecoderCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defined;->getDiskCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defined;->getErrorFactory()Lkotlin/jvm/functions/Function1;
SPLcoil3/request/ImageRequest$Defined;->getFallbackFactory()Lkotlin/jvm/functions/Function1;
SPLcoil3/request/ImageRequest$Defined;->getFetcherCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defined;->getFileSystem()Lokio/FileSystem;
SPLcoil3/request/ImageRequest$Defined;->getInterceptorCoroutineContext()Lkotlin/coroutines/CoroutineContext;
SPLcoil3/request/ImageRequest$Defined;->getMemoryCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defined;->getNetworkCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/ImageRequest$Defined;->getPlaceholderFactory()Lkotlin/jvm/functions/Function1;
SPLcoil3/request/ImageRequest$Defined;->getPrecision()Lcoil3/size/Precision;
SPLcoil3/request/ImageRequest$Defined;->getScale()Lcoil3/size/Scale;
SPLcoil3/request/ImageRequest$Defined;->getSizeResolver()Lcoil3/size/SizeResolver;
Lcoil3/request/ImageRequest$Listener;
Lcoil3/request/ImageRequestsKt;
SPLcoil3/request/ImageRequestsKt;-><clinit>()V
SPLcoil3/request/ImageRequestsKt;->crossfade(Lcoil3/ImageLoader$Builder;Z)Lcoil3/ImageLoader$Builder;
SPLcoil3/request/ImageRequestsKt;->getMaxBitmapSize(Lcoil3/request/Options;)Lcoil3/size/Size;
SPLcoil3/request/ImageRequestsKt;->getTransformations(Lcoil3/request/ImageRequest;)Ljava/util/List;
Lcoil3/request/ImageRequests_androidKt;
SPLcoil3/request/ImageRequests_androidKt;-><clinit>()V
SPLcoil3/request/ImageRequests_androidKt;->crossfade(Lcoil3/ImageLoader$Builder;I)Lcoil3/ImageLoader$Builder;
SPLcoil3/request/ImageRequests_androidKt;->getAllowHardware(Lcoil3/request/ImageRequest;)Z
SPLcoil3/request/ImageRequests_androidKt;->getAllowRgb565(Lcoil3/request/ImageRequest;)Z
SPLcoil3/request/ImageRequests_androidKt;->getAllowRgb565(Lcoil3/request/Options;)Z
SPLcoil3/request/ImageRequests_androidKt;->getBitmapConfig(Lcoil3/request/ImageRequest;)Landroid/graphics/Bitmap$Config;
SPLcoil3/request/ImageRequests_androidKt;->getBitmapConfig(Lcoil3/request/Options;)Landroid/graphics/Bitmap$Config;
SPLcoil3/request/ImageRequests_androidKt;->getColorSpace(Lcoil3/request/Options;)Landroid/graphics/ColorSpace;
SPLcoil3/request/ImageRequests_androidKt;->getLifecycle(Lcoil3/request/ImageRequest;)Landroidx/lifecycle/Lifecycle;
SPLcoil3/request/ImageRequests_androidKt;->getPremultipliedAlpha(Lcoil3/request/Options;)Z
SPLcoil3/request/ImageRequests_androidKt;->getTransitionFactory(Lcoil3/request/ImageRequest;)Lcoil3/transition/Transition$Factory;
SPLcoil3/request/ImageRequests_androidKt;->newCrossfadeTransitionFactory(I)Lcoil3/transition/Transition$Factory;
SPLcoil3/request/ImageRequests_androidKt;->transitionFactory(Lcoil3/ImageLoader$Builder;Lcoil3/transition/Transition$Factory;)Lcoil3/ImageLoader$Builder;
Lcoil3/request/ImageResult;
Lcoil3/request/NullRequestData;
SPLcoil3/request/NullRequestData;-><clinit>()V
SPLcoil3/request/NullRequestData;-><init>()V
Lcoil3/request/NullRequestDataException;
Lcoil3/request/Options;
SPLcoil3/request/Options;-><init>(Landroid/content/Context;Lcoil3/size/Size;Lcoil3/size/Scale;Lcoil3/size/Precision;Ljava/lang/String;Lokio/FileSystem;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/request/CachePolicy;Lcoil3/Extras;)V
SPLcoil3/request/Options;->getContext()Landroid/content/Context;
SPLcoil3/request/Options;->getDiskCacheKey()Ljava/lang/String;
SPLcoil3/request/Options;->getDiskCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/Options;->getExtras()Lcoil3/Extras;
SPLcoil3/request/Options;->getNetworkCachePolicy()Lcoil3/request/CachePolicy;
SPLcoil3/request/Options;->getScale()Lcoil3/size/Scale;
SPLcoil3/request/Options;->getSize()Lcoil3/size/Size;
Lcoil3/request/RequestDelegate;
Lcoil3/request/RequestDelegate$-CC;
SPLcoil3/request/RequestDelegate$-CC;->$default$assertActive(Lcoil3/request/RequestDelegate;)V
SPLcoil3/request/RequestDelegate$-CC;->$default$complete(Lcoil3/request/RequestDelegate;)V
SPLcoil3/request/RequestDelegate$-CC;->$default$start(Lcoil3/request/RequestDelegate;)V
Lcoil3/request/RequestService;
Lcoil3/request/RequestService_androidKt;
SPLcoil3/request/RequestService_androidKt;->RequestService(Lcoil3/ImageLoader;Lcoil3/util/SystemCallbacks;Lcoil3/util/Logger;)Lcoil3/request/RequestService;
Lcoil3/request/SuccessResult;
SPLcoil3/request/SuccessResult;-><init>(Lcoil3/Image;Lcoil3/request/ImageRequest;Lcoil3/decode/DataSource;Lcoil3/memory/MemoryCache$Key;Ljava/lang/String;ZZ)V
SPLcoil3/request/SuccessResult;->getDataSource()Lcoil3/decode/DataSource;
SPLcoil3/request/SuccessResult;->getImage()Lcoil3/Image;
SPLcoil3/request/SuccessResult;->getMemoryCacheKey()Lcoil3/memory/MemoryCache$Key;
SPLcoil3/request/SuccessResult;->getRequest()Lcoil3/request/ImageRequest;
SPLcoil3/request/SuccessResult;->isPlaceholderCached()Z
Lcoil3/size/Dimension;
Lcoil3/size/Dimension$Pixels;
SPLcoil3/size/Dimension$Pixels;-><init>(I)V
SPLcoil3/size/Dimension$Pixels;->box-impl(I)Lcoil3/size/Dimension$Pixels;
SPLcoil3/size/Dimension$Pixels;->constructor-impl(I)I
SPLcoil3/size/Dimension$Pixels;->equals(Ljava/lang/Object;)Z
SPLcoil3/size/Dimension$Pixels;->equals-impl(ILjava/lang/Object;)Z
SPLcoil3/size/Dimension$Pixels;->unbox-impl()I
Lcoil3/size/Dimension$Undefined;
SPLcoil3/size/Dimension$Undefined;-><clinit>()V
SPLcoil3/size/Dimension$Undefined;-><init>()V
Lcoil3/size/DimensionKt;
SPLcoil3/size/DimensionKt;->Dimension(I)I
Lcoil3/size/Precision;
SPLcoil3/size/Precision;->$values()[Lcoil3/size/Precision;
SPLcoil3/size/Precision;-><clinit>()V
SPLcoil3/size/Precision;-><init>(Ljava/lang/String;I)V
Lcoil3/size/RealSizeResolver;
SPLcoil3/size/RealSizeResolver;-><init>(Lcoil3/size/Size;)V
Lcoil3/size/Scale;
SPLcoil3/size/Scale;->$values()[Lcoil3/size/Scale;
SPLcoil3/size/Scale;-><clinit>()V
SPLcoil3/size/Scale;-><init>(Ljava/lang/String;I)V
SPLcoil3/size/Scale;->values()[Lcoil3/size/Scale;
Lcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m$1()Landroid/graphics/Bitmap$Config;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m$1(Landroid/graphics/ImageDecoder;I)V
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m()Landroid/graphics/Bitmap$Config;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m()Ljava/lang/Class;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder$ImageInfo;)Landroid/util/Size;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder$Source;Landroid/graphics/ImageDecoder$OnHeaderDecodedListener;)Landroid/graphics/Bitmap;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder;I)V
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder;II)V
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder;Landroid/graphics/ImageDecoder$OnPartialImageListener;)V
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/graphics/ImageDecoder;Z)V
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Landroid/net/ConnectivityManager;)Landroid/net/Network;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Ljava/io/File;)Landroid/graphics/ImageDecoder$Source;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Ljava/lang/Object;)Landroid/graphics/ColorSpace;
SPLcoil3/size/ScaleDrawable$$ExternalSyntheticApiModelOutline0;->m(Ljava/lang/Object;)Landroid/graphics/ImageDecoder$OnHeaderDecodedListener;
Lcoil3/size/Size;
SPLcoil3/size/Size;-><clinit>()V
SPLcoil3/size/Size;-><init>(Lcoil3/size/Dimension;Lcoil3/size/Dimension;)V
SPLcoil3/size/Size;->equals(Ljava/lang/Object;)Z
SPLcoil3/size/Size;->getHeight()Lcoil3/size/Dimension;
SPLcoil3/size/Size;->getWidth()Lcoil3/size/Dimension;
Lcoil3/size/Size$Companion;
SPLcoil3/size/Size$Companion;-><init>()V
SPLcoil3/size/Size$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/size/SizeKt;
SPLcoil3/size/SizeKt;->Size(II)Lcoil3/size/Size;
SPLcoil3/size/SizeKt;->isOriginal(Lcoil3/size/Size;)Z
Lcoil3/size/SizeResolver;
SPLcoil3/size/SizeResolver;-><clinit>()V
Lcoil3/size/SizeResolver$Companion;
SPLcoil3/size/SizeResolver$Companion;-><clinit>()V
SPLcoil3/size/SizeResolver$Companion;-><init>()V
Lcoil3/size/SizeResolverKt;
SPLcoil3/size/SizeResolverKt;->SizeResolver(Lcoil3/size/Size;)Lcoil3/size/SizeResolver;
Lcoil3/size/ViewSizeResolver;
Lcoil3/target/Target;
Lcoil3/target/ViewTarget;
Lcoil3/transition/CrossfadeTransition;
SPLcoil3/transition/CrossfadeTransition;-><init>(Lcoil3/transition/TransitionTarget;Lcoil3/request/ImageResult;IZ)V
SPLcoil3/transition/CrossfadeTransition;->getDurationMillis()I
SPLcoil3/transition/CrossfadeTransition;->getPreferExactIntrinsicSize()Z
Lcoil3/transition/CrossfadeTransition$Factory;
SPLcoil3/transition/CrossfadeTransition$Factory;-><init>(IZ)V
SPLcoil3/transition/CrossfadeTransition$Factory;-><init>(IZILkotlin/jvm/internal/DefaultConstructorMarker;)V
SPLcoil3/transition/CrossfadeTransition$Factory;->create(Lcoil3/transition/TransitionTarget;Lcoil3/request/ImageResult;)Lcoil3/transition/Transition;
Lcoil3/transition/NoneTransition$Factory;
SPLcoil3/transition/NoneTransition$Factory;-><init>()V
Lcoil3/transition/Transition;
Lcoil3/transition/Transition$Factory;
SPLcoil3/transition/Transition$Factory;-><clinit>()V
Lcoil3/transition/Transition$Factory$Companion;
SPLcoil3/transition/Transition$Factory$Companion;-><clinit>()V
SPLcoil3/transition/Transition$Factory$Companion;-><init>()V
Lcoil3/transition/TransitionTarget;
Lcoil3/util/AndroidSystemCallbacks;
SPLcoil3/util/AndroidSystemCallbacks;-><clinit>()V
SPLcoil3/util/AndroidSystemCallbacks;-><init>(Lcoil3/RealImageLoader;)V
SPLcoil3/util/AndroidSystemCallbacks;->registerMemoryPressureCallbacks()V
Lcoil3/util/AndroidSystemCallbacks$Companion;
SPLcoil3/util/AndroidSystemCallbacks$Companion;-><init>()V
SPLcoil3/util/AndroidSystemCallbacks$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Lcoil3/util/BitmapsKt;
SPLcoil3/util/BitmapsKt;->getAllocationByteCountCompat(Landroid/graphics/Bitmap;)I
SPLcoil3/util/BitmapsKt;->isHardware(Landroid/graphics/Bitmap$Config;)Z
Lcoil3/util/Collections_jvmCommonKt;
SPLcoil3/util/Collections_jvmCommonKt;->LruMutableMap$default(IFILjava/lang/Object;)Ljava/util/Map;
SPLcoil3/util/Collections_jvmCommonKt;->LruMutableMap(IF)Ljava/util/Map;
SPLcoil3/util/Collections_jvmCommonKt;->toImmutableList(Ljava/util/List;)Ljava/util/List;
SPLcoil3/util/Collections_jvmCommonKt;->toImmutableMap(Ljava/util/Map;)Ljava/util/Map;
Lcoil3/util/ContextsKt;
SPLcoil3/util/ContextsKt;->getApplication(Landroid/content/Context;)Landroid/content/Context;
SPLcoil3/util/ContextsKt;->totalAvailableMemoryBytes(Landroid/content/Context;)J
Lcoil3/util/Coroutines_nonJsCommonKt;
SPLcoil3/util/Coroutines_nonJsCommonKt;->ioCoroutineDispatcher()Lkotlinx/coroutines/CoroutineDispatcher;
Lcoil3/util/DecoderServiceLoaderTarget;
Lcoil3/util/DecoderServiceLoaderTarget$-CC;
SPLcoil3/util/DecoderServiceLoaderTarget$-CC;->$default$priority(Lcoil3/util/DecoderServiceLoaderTarget;)I
Lcoil3/util/FetcherServiceLoaderTarget;
Lcoil3/util/FetcherServiceLoaderTarget$-CC;
SPLcoil3/util/FetcherServiceLoaderTarget$-CC;->$default$priority(Lcoil3/util/FetcherServiceLoaderTarget;)I
Lcoil3/util/FileSystemsKt;
SPLcoil3/util/FileSystemsKt;->createFile$default(Lokio/FileSystem;Lokio/Path;ZILjava/lang/Object;)V
SPLcoil3/util/FileSystemsKt;->createFile(Lokio/FileSystem;Lokio/Path;Z)V
Lcoil3/util/FileSystems_nonJsCommonKt;
SPLcoil3/util/FileSystems_nonJsCommonKt;->defaultFileSystem()Lokio/FileSystem;
Lcoil3/util/HardwareBitmapService;
Lcoil3/util/HardwareBitmapsKt;
SPLcoil3/util/HardwareBitmapsKt;-><clinit>()V
SPLcoil3/util/HardwareBitmapsKt;->HardwareBitmapService(Lcoil3/util/Logger;)Lcoil3/util/HardwareBitmapService;
Lcoil3/util/ImmutableHardwareBitmapService;
SPLcoil3/util/ImmutableHardwareBitmapService;-><init>(Z)V
SPLcoil3/util/ImmutableHardwareBitmapService;->allowHardwareMainThread(Lcoil3/size/Size;)Z
SPLcoil3/util/ImmutableHardwareBitmapService;->allowHardwareWorkerThread()Z
Lcoil3/util/IntPair;
SPLcoil3/util/IntPair;->constructor-impl(II)J
SPLcoil3/util/IntPair;->constructor-impl(J)J
SPLcoil3/util/IntPair;->getFirst-impl(J)I
SPLcoil3/util/IntPair;->getSecond-impl(J)I
Lcoil3/util/Logger;
Lcoil3/util/LruCache;
SPLcoil3/util/LruCache;-><init>(J)V
SPLcoil3/util/LruCache;->get(Ljava/lang/Object;)Ljava/lang/Object;
SPLcoil3/util/LruCache;->getMaxSize()J
SPLcoil3/util/LruCache;->getSize()J
SPLcoil3/util/LruCache;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
SPLcoil3/util/LruCache;->safeSizeOf(Ljava/lang/Object;Ljava/lang/Object;)J
SPLcoil3/util/LruCache;->trimToSize(J)V
Lcoil3/util/ServiceLoaderComponentRegistry;
SPLcoil3/util/ServiceLoaderComponentRegistry;->$r8$lambda$rCNNmXdWcAQ1pDqLHEKqVpwkpK0()Ljava/util/List;
SPLcoil3/util/ServiceLoaderComponentRegistry;->$r8$lambda$vrh-lrp-qgWYWycHjerBHzh9snk()Ljava/util/List;
SPLcoil3/util/ServiceLoaderComponentRegistry;-><clinit>()V
SPLcoil3/util/ServiceLoaderComponentRegistry;-><init>()V
SPLcoil3/util/ServiceLoaderComponentRegistry;->decoders_delegate$lambda$1()Ljava/util/List;
SPLcoil3/util/ServiceLoaderComponentRegistry;->fetchers_delegate$lambda$0()Ljava/util/List;
SPLcoil3/util/ServiceLoaderComponentRegistry;->getDecoders()Ljava/util/List;
SPLcoil3/util/ServiceLoaderComponentRegistry;->getFetchers()Ljava/util/List;
Lcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda0;
SPLcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda0;-><init>()V
SPLcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda0;->invoke()Ljava/lang/Object;
Lcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda1;
SPLcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda1;-><init>()V
SPLcoil3/util/ServiceLoaderComponentRegistry$$ExternalSyntheticLambda1;->invoke()Ljava/lang/Object;
Lcoil3/util/SystemCallbacks;
Lcoil3/util/SystemCallbacksKt;
SPLcoil3/util/SystemCallbacksKt;->SystemCallbacks(Lcoil3/RealImageLoader;)Lcoil3/util/SystemCallbacks;
Lcoil3/util/UtilsKt;
SPLcoil3/util/UtilsKt;-><clinit>()V
SPLcoil3/util/UtilsKt;->closeQuietly(Ljava/io/Closeable;)V
SPLcoil3/util/UtilsKt;->closeQuietly(Ljava/lang/AutoCloseable;)V
SPLcoil3/util/UtilsKt;->getDispatcher(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineDispatcher;
SPLcoil3/util/UtilsKt;->getEMPTY_IMAGE_FACTORY()Lkotlin/jvm/functions/Function1;
SPLcoil3/util/UtilsKt;->getEventListener(Lcoil3/intercept/Interceptor$Chain;)Lcoil3/EventListener;
SPLcoil3/util/UtilsKt;->isFileUri(Lcoil3/Uri;)Z
SPLcoil3/util/UtilsKt;->isMinOrMax(I)Z
SPLcoil3/util/UtilsKt;->isPlaceholderCached(Lcoil3/intercept/Interceptor$Chain;)Z
SPLcoil3/util/UtilsKt;->key(Lcoil3/ComponentRegistry;Ljava/lang/Object;Lcoil3/request/Options;Lcoil3/util/Logger;Ljava/lang/String;)Ljava/lang/String;
Lcoil3/util/UtilsKt$EMPTY_IMAGE_FACTORY$1;
SPLcoil3/util/UtilsKt$EMPTY_IMAGE_FACTORY$1;-><clinit>()V
SPLcoil3/util/UtilsKt$EMPTY_IMAGE_FACTORY$1;-><init>()V
SPLcoil3/util/UtilsKt$EMPTY_IMAGE_FACTORY$1;->invoke(Lcoil3/request/ImageRequest;)Ljava/lang/Void;
SPLcoil3/util/UtilsKt$EMPTY_IMAGE_FACTORY$1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
Lcoil3/util/Utils_androidKt;
SPLcoil3/util/Utils_androidKt;-><clinit>()V
SPLcoil3/util/Utils_androidKt;->getDEFAULT_BITMAP_CONFIG()Landroid/graphics/Bitmap$Config;
SPLcoil3/util/Utils_androidKt;->getNULL_COLOR_SPACE()Landroid/graphics/ColorSpace;
SPLcoil3/util/Utils_androidKt;->prepareToDraw(Lcoil3/Image;)V
# Baseline profile rules for androidx.compose.material.ripple
# =============================================
HSPLandroidx/compose/material/ripple/AndroidRippleIndicationInstance;->**(**)**
HSPLandroidx/compose/material/ripple/PlatformRipple;->**(**)**
HSPLandroidx/compose/material/ripple/DebugRippleTheme;->**(**)**
HSPLandroidx/compose/material/ripple/Ripple;->**(**)**
HSPLandroidx/compose/material/ripple/RippleAlpha;->**(**)**
HSPLandroidx/compose/material/ripple/RippleContainer;->**(**)**
HSPLandroidx/compose/material/ripple/RippleHostMap;->**(**)**
HSPLandroidx/compose/material/ripple/UnprojectedRipple;->**(**)**
HSPLandroidx/compose/material/ripple/RippleHostView;->**(**)**
HSPLandroidx/compose/material/ripple/RippleIndicationInstance;->**(**)**
HSPLandroidx/compose/material/ripple/RippleKt;->**(**)**
HSPLandroidx/compose/material/ripple/RippleHostMap;->**(**)**
HSPLandroidx/compose/material/ripple/RippleContainer;->**(**)**
HSPLandroidx/compose/material/ripple/RippleAlpha;->**(**)**
HSPLandroidx/compose/material/ripple/RippleThemeKt**->**(**)**
HSPLandroidx/compose/material/ripple/StateLayer;->**(**)**

Landroidx/compose/material/ripple/*;

# Baseline profile rules for androidx.compose.animation.core
# =============================================
# In practice it seems like almost every class in animation/core ends up getting loaded in even a
# relatively small sample, and most end up getting marked as "HSP". Since Animation is a high value
# target for performance - fade in, scroll, etc we are going to be liberal in the animation profile
# rules and just mark the entire module.

HSPLandroidx/compose/animation/core/**->**(**)**

Landroidx/compose/animation/core/**;

# Baseline profile rules for androidx.compose.foundation.layout
# =============================================
# Layout is incredibly important for performance, and represents many hot code paths. For now, we
# will include all of the layout namespace
HSPLandroidx/compose/foundation/layout/**->**(**)**
Landroidx/compose/foundation/layout/**;
# Baseline profile rules for androidx.compose.foundation
# =============================================
#
# Include all methods in common top level classes
HSPLandroidx/compose/foundation/AbstractClickable**->**(**)**
HSPLandroidx/compose/foundation/AndroidEdgeEffectOverscrollEffect**->**(**)**
HSPLandroidx/compose/foundation/AndroidOverscroll_**->**(**)**
HSPLandroidx/compose/foundation/BackgroundElement;->**(**)**
HSPLandroidx/compose/foundation/BackgroundNode;->**(**)**
HSPLandroidx/compose/foundation/BorderKt**->**(**)**
HSPLandroidx/compose/foundation/BorderStroke;->**(**)**
HSPLandroidx/compose/foundation/CanvasKt**->**(**)**
HSPLandroidx/compose/foundation/Clickable**->**(**)**
HSPLandroidx/compose/foundation/DrawOverscrollModifier;->**(**)**
HSPLandroidx/compose/foundation/EdgeEffectWrapper;->**(**)**
HSPLandroidx/compose/foundation/Focusable**->**(**)**
HSPLandroidx/compose/foundation/FocusedBounds**->**(**)**
HSPLandroidx/compose/foundation/Hoverable**->**(**)**
HSPLandroidx/compose/foundation/ImageKt**->**(**)**
HSPLandroidx/compose/foundation/IndicationKt**->**(**)**
HSPLandroidx/compose/foundation/IndicationModifier;->**(**)**
HSPLandroidx/compose/foundation/MutatePriority;->**(**)**
HSPLandroidx/compose/foundation/MutatorMutex**->**(**)**
HSPLandroidx/compose/foundation/PinnableParentConsumer;->**(**)**
HSPLandroidx/compose/foundation/ScrollKt**->**(**)**
HSPLandroidx/compose/foundation/ScrollState**->**(**)**
HSPLandroidx/compose/foundation/ScrollingLayoutModifier**->**(**)**
#
# Include everything inside of the gestures namespace
HSPLandroidx/compose/foundation/gestures/**->**(**)**
#
# Include everything inside of the interaction namespace
HSPLandroidx/compose/foundation/interaction/*;->**(**)**

# Include everything inside of the lazy namespaces
HSPLandroidx/compose/foundation/lazy/**->**(**)**

# Include everything inside relocation namespace
HSPLandroidx/compose/foundation/relocation/**->**(**)**

# Include everything inside selection namespace
HSPLandroidx/compose/foundation/selection/**->**(**)**

#
# common shape classes
HSPLandroidx/compose/foundation/shape/CornerBasedShape;->**(**)**
HSPLandroidx/compose/foundation/shape/CornerSizeKt;->**(**)**
HSPLandroidx/compose/foundation/shape/DpCornerSize;->**(**)**
HSPLandroidx/compose/foundation/shape/RoundedCornerShape;->**(**)**
HSPLandroidx/compose/foundation/shape/PercentCornerSize;->**(**)**
#

# Include everything inside of the text namespace
HSPLandroidx/compose/foundation/text/*;->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/**->**(**)**
HSPLandroidx/compose/foundation/text/selection/SimpleLayoutKt**->**(**)**
HSPLandroidx/compose/foundation/text/selection/TextFieldSelectionManager;->**(**)**

#
# Include all of foundation
Landroidx/compose/foundation/**;

# Baseline profile rules for androidx.compose.ui.unit
# =============================================
# everything in unit is relatively small and in the hot path, so we just add everything
HSPLandroidx/compose/ui/unit/**->**(**)**
Landroidx/compose/ui/unit/**

# Baseline profile rules for androidx.compose.ui.graphics
# =============================================
HSPLandroidx/compose/ui/graphics/AndroidCanvas**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidColorFilter_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidImageBitmap;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidImageBitmap_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidMatrixConversions_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPaint;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPaint_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPath;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPath_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPathMeasure;->**(**)**
HSPLandroidx/compose/ui/graphics/BlendMode**->**(**)**
HSPLandroidx/compose/ui/graphics/BlockGraphicsLayerModifier**->**(**)**
HSPLandroidx/compose/ui/graphics/Brush;->**(**)**
HSPLandroidx/compose/ui/graphics/Canvas$DefaultImpls;->**(**)**
HSPLandroidx/compose/ui/graphics/CanvasHolder;->**(**)**
HSPLandroidx/compose/ui/graphics/CanvasKt;->**(**)**
HSPLandroidx/compose/ui/graphics/CanvasUtils;->**(**)**
HSPLandroidx/compose/ui/graphics/CanvasZHelper**->**(**)**
HSPLandroidx/compose/ui/graphics/ClipOp**->**(**)**
HSPLandroidx/compose/ui/graphics/Color**->**(**)**
HSPLandroidx/compose/ui/graphics/ColorFilter**->**(**)**
HSPLandroidx/compose/ui/graphics/ColorKt;->**(**)**
HSPLandroidx/compose/ui/graphics/Float16**->**(**)**
HSPLandroidx/compose/ui/graphics/GraphicsLayerModifierKt;->**(**)**
HSPLandroidx/compose/ui/graphics/Matrix;->**(**)**
HSPLandroidx/compose/ui/graphics/ImageBitmapConfig**->**(**)**
HSPLandroidx/compose/ui/graphics/MatrixKt;->**(**)**
HSPLandroidx/compose/ui/graphics/Outline**->**(**)**
HSPLandroidx/compose/ui/graphics/PaintingStyle**->**(**)**
HSPLandroidx/compose/ui/graphics/PathFillType**->**(**)**
HSPLandroidx/compose/ui/graphics/RectangleShapeKt;->**(**)**
HSPLandroidx/compose/ui/graphics/RectHelper_androidKt;->**(**)**
HSPLandroidx/compose/ui/graphics/ReusableGraphicsLayerScope;->**(**)**
HSPLandroidx/compose/ui/graphics/Shadow;->**(**)**
HSPLandroidx/compose/ui/graphics/SimpleGraphicsLayerModifier**->**(**)**
HSPLandroidx/compose/ui/graphics/SolidColor;->**(**)**
HSPLandroidx/compose/ui/graphics/StrokeCap**->**(**)**
HSPLandroidx/compose/ui/graphics/StrokeJoin**->**(**)**
HSPLandroidx/compose/ui/graphics/TransformOrigin**->**(**)**
HSPLandroidx/compose/ui/graphics/painter/BitmapPainter;->**(**)**
HSPLandroidx/compose/ui/graphics/painter/Painter**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/DrawCache;->**(**)**
#
# All of colorspace
HSPLandroidx/compose/ui/graphics/colorspace/**->**(**)**
#
# All of drawscope
HSPLandroidx/compose/ui/graphics/drawscope/**->**(**)**
#
# All of layer
HSPLandroidx/compose/ui/graphics/layer/**->**(**)**
#
# Vector stuff
HSPLandroidx/compose/ui/graphics/vector/DefaultVectorOverride;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/DrawCache;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/GroupComponent;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/ImageVector**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathBuilder;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathComponent;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathNode**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathParser**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/Stack;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VNode;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorApplier;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorComponent;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorComposeKt**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorGroup;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorKt;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorPainter**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorPath;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/compat/AndroidVectorResources;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt;->**(**)**
#
# Assume ~all classes will get loaded
Landroidx/compose/ui/graphics/**;
# Baseline profile rules for androidx.compose.ui.util
# =============================================
HSPLandroidx/compose/ui/util/MathHelpersKt;->lerp(FFF)F
Landroidx/compose/ui/util/MathHelpersKt;

# Baseline profile rules for androidx.compose.ui.text
# =============================================

HSPLandroidx/compose/ui/text/AndroidParagraph**->**(**)**
HSPLandroidx/compose/ui/text/AnnotatedString**->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraph;->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraphIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/ParagraphInfo;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphIntrinsicInfo;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyle;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyleKt;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphKt;->**(**)**
HSPLandroidx/compose/ui/text/PlatformTextStyle;->**(**)**
HSPLandroidx/compose/ui/text/SpanStyle;->**(**)**
HSPLandroidx/compose/ui/text/SpanStyleKt;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutInput;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutResult;->**(**)**
HSPLandroidx/compose/ui/text/TextPainter;->**(**)**
HSPLandroidx/compose/ui/text/TextRange**->**(**)**
HSPLandroidx/compose/ui/text/TextStyle**->**(**)**
HSPLandroidx/compose/ui/text/android/BoringLayoutFactory**->**(**)**
HSPLandroidx/compose/ui/text/android/CharSequenceCharacterIterator;->**(**)**
HSPLandroidx/compose/ui/text/android/LayoutIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/android/Paint**->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutFactory**->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutParams;->**(**)**
HSPLandroidx/compose/ui/text/android/TextAlignmentAdapter;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayout;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayoutKt;->**(**)**
HSPLandroidx/compose/ui/text/android/style/BaselineShiftSpan;->**(**)**
HSPLandroidx/compose/ui/text/android/style/LetterSpacingSpanPx;->**(**)**
HSPLandroidx/compose/ui/text/android/style/LineHeightSpan;->**(**)**
HSPLandroidx/compose/ui/text/android/style/TypefaceSpan;->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontLoader;->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontResolveInterceptor**->**(**)**
HSPLandroidx/compose/ui/text/font/AsyncTypefaceCache;->**(**)**
HSPLandroidx/compose/ui/text/font/DefaultFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FileBasedFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FontFamily**->**(**)**
HSPLandroidx/compose/ui/text/font/FontFamilyResolverImpl**->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamilyTypefaceAdapter**->**(**)**
HSPLandroidx/compose/ui/text/font/FontKt;->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FontMatcher;->**(**)**
HSPLandroidx/compose/ui/text/font/FontStyle;->**(**)**
HSPLandroidx/compose/ui/text/font/FontSynthesis;->**(**)**
HSPLandroidx/compose/ui/text/font/FontWeight**->**(**)**
HSPLandroidx/compose/ui/text/font/PlatformFontFamilyTypefaceAdapter;->**(**)**
HSPLandroidx/compose/ui/text/font/PlatformTypefacesApi28;->**(**)**
HSPLandroidx/compose/ui/text/font/GenericFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/SystemFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceRequest**->**(**)**
HSPLandroidx/compose/ui/text/font/ResourceFont;->**(**)**
HSPLandroidx/compose/ui/text/font/SystemFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/input/InputMethodManagerImpl**->**(**)**
HSPLandroidx/compose/ui/text/input/EditProcessor;->**(**)**
HSPLandroidx/compose/ui/text/input/EditingBuffer;->**(**)**
HSPLandroidx/compose/ui/text/input/ImeAction**->**(**)**
HSPLandroidx/compose/ui/text/input/ImeOptions**->**(**)**
HSPLandroidx/compose/ui/text/input/KeyboardType**->**(**)**
HSPLandroidx/compose/ui/text/input/TextFieldValue;->**(**)**
HSPLandroidx/compose/ui/text/input/TextInputService**->**(**)**
HSPLandroidx/compose/ui/text/input/TransformedText;->**(**)**
HSPLandroidx/compose/ui/text/intl/AndroidLocale**->**(**)**
HSPLandroidx/compose/ui/text/intl/Locale**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidAccessibility**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraphIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraph_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidTextPaint;->**(**)**
HSPLandroidx/compose/ui/text/platform/DefaultImpl;->**(**)**
HSPLandroidx/compose/ui/text/platform/DispatcherKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/EmojiCompatStatus;->**(**)**
HSPLandroidx/compose/ui/text/platform/ImmutableBool;->**(**)**
HSPLandroidx/compose/ui/text/platform/SynchronizedObject;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceDirtyTracker;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceAdapter;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceAdapterHelperMethods;->**(**)**
HSPLandroidx/compose/ui/text/platform/URLSpanCache;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/SpanRange;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/SpannableExtensions_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/TextPaintExtensions_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/style/BaselineShift**->**(**)**
HSPLandroidx/compose/ui/text/style/ColorStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/LineHeightStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/LineHeightStyle$Alignment;->**(**)**
HSPLandroidx/compose/ui/text/style/ResolvedTextDirection;->**(**)**
HSPLandroidx/compose/ui/text/style/TextAlign;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDecoration;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDrawStyle**->**(**)**
HSPLandroidx/compose/ui/text/style/TextDirection;->**(**)**
HSPLandroidx/compose/ui/text/style/TextForegroundStyle**;->**(**)**
HSPLandroidx/compose/ui/text/style/TextGeometricTransform;->**(**)**
HSPLandroidx/compose/ui/text/style/TextIndent;->**(**)**
HSPLandroidx/compose/ui/text/style/TextMotion;->**(**)**

Landroidx/compose/ui/text/**;

#
# We rely heavily on some text methods in kotlin stdlib, so makes sense to include them here
HSPLkotlin/text/CharsKt__CharJVMKt;->isWhitespace(C)Z
HSPLkotlin/text/MatcherMatchResult$groups$1;-><init>(Lkotlin/text/MatcherMatchResult;)V
HSPLkotlin/text/MatcherMatchResult;-><init>(Ljava/util/regex/Matcher;Ljava/lang/CharSequence;)V
HSPLkotlin/text/MatcherMatchResult;->getMatchResult()Ljava/util/regex/MatchResult;
HSPLkotlin/text/MatcherMatchResult;->getRange()Lkotlin/ranges/IntRange;
HSPLkotlin/text/MatcherMatchResult;->getValue()Ljava/lang/String;
HSPLkotlin/text/MatcherMatchResult;->next()Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex$Companion;-><init>()V
HSPLkotlin/text/Regex$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/text/Regex$findAll$1;-><init>(Lkotlin/text/Regex;Ljava/lang/CharSequence;I)V
HSPLkotlin/text/Regex$findAll$1;->invoke()Ljava/lang/Object;
HSPLkotlin/text/Regex$findAll$1;->invoke()Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex$findAll$2;-><init>()V
HSPLkotlin/text/Regex$findAll$2;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/text/Regex$findAll$2;->invoke(Lkotlin/text/MatchResult;)Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex;-><init>(Ljava/lang/String;)V
HSPLkotlin/text/Regex;-><init>(Ljava/util/regex/Pattern;)V
HSPLkotlin/text/Regex;->find(Ljava/lang/CharSequence;I)Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex;->findAll$default(Lkotlin/text/Regex;Ljava/lang/CharSequence;IILjava/lang/Object;)Lkotlin/sequences/Sequence;
HSPLkotlin/text/Regex;->findAll(Ljava/lang/CharSequence;I)Lkotlin/sequences/Sequence;
HSPLkotlin/text/RegexKt;->access$findNext(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Lkotlin/text/MatchResult;
HSPLkotlin/text/RegexKt;->access$range(Ljava/util/regex/MatchResult;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/RegexKt;->findNext(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Lkotlin/text/MatchResult;
HSPLkotlin/text/RegexKt;->range(Ljava/util/regex/MatchResult;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/StringsKt__StringsJVMKt;->endsWith$default(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->endsWith(Ljava/lang/String;Ljava/lang/String;Z)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->isBlank(Ljava/lang/CharSequence;)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->repeat(Ljava/lang/CharSequence;I)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->endsWith$default(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z
HSPLkotlin/text/StringsKt__StringsKt;->endsWith(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Z)Z
HSPLkotlin/text/StringsKt__StringsKt;->getIndices(Ljava/lang/CharSequence;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/StringsKt__StringsKt;->getLastIndex(Ljava/lang/CharSequence;)I
HSPLkotlin/text/StringsKt__StringsKt;->lastIndexOf$default(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I
HSPLkotlin/text/StringsKt__StringsKt;->lastIndexOf(Ljava/lang/CharSequence;CIZ)I
HSPLkotlin/text/StringsKt__StringsKt;->substring(Ljava/lang/String;Lkotlin/ranges/IntRange;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->substringAfterLast$default(Ljava/lang/String;CLjava/lang/String;ILjava/lang/Object;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->substringAfterLast(Ljava/lang/String;CLjava/lang/String;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->trim(Ljava/lang/String;[C)Ljava/lang/String;
HSPLkotlin/text/StringsKt___StringsKt;->first(Ljava/lang/CharSequence;)C
HSPLkotlin/text/StringsKt___StringsKt;->slice(Ljava/lang/String;Lkotlin/ranges/IntRange;)Ljava/lang/String;

# Baseline Profile rules for lifecycle-runtime

HPLandroidx/lifecycle/LifecycleRegistry;->backwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;-><init>(Landroidx/lifecycle/LifecycleObserver;Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;->dispatchEvent(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;Z)V
HSPLandroidx/lifecycle/LifecycleRegistry;->addObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->calculateTargetState(Landroidx/lifecycle/LifecycleObserver;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->enforceMainThreadIfNeeded(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->forwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->getCurrentState()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->handleLifecycleEvent(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->isSynced()Z
HSPLandroidx/lifecycle/LifecycleRegistry;->min(Landroidx/lifecycle/Lifecycle$State;Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->moveToState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->popParentState()V
HSPLandroidx/lifecycle/LifecycleRegistry;->pushParentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->removeObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->setCurrentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->sync()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;-><init>()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->registerIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;-><init>()V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroid/app/Activity;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchCreate(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchResume(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchStart(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->injectIfNeededIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;->onActivityCreated(Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment;->onResume()V
HSPLandroidx/lifecycle/ReportFragment;->onStart()V
HSPLandroidx/lifecycle/ViewTreeLifecycleOwner;->set(Landroid/view/View;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/ViewTreeViewModelStoreOwner;->set(Landroid/view/View;Landroidx/lifecycle/ViewModelStoreOwner;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPrePaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment;->onDestroy()V
PLandroidx/lifecycle/ReportFragment;->onPause()V
PLandroidx/lifecycle/ReportFragment;->onStop()V

# Baseline Profiles for lifecycle-common

HPLandroidx/lifecycle/Lifecycle$Event;->downFrom(Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;-><init>(Ljava/util/Map;)V
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;->invokeCallbacks(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;->invokeMethodsForEvent(Ljava/util/List;Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;-><init>(ILjava/lang/reflect/Method;)V
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;->hashCode()I
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;->invokeCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache;-><clinit>()V
HSPLandroidx/lifecycle/ClassesInfoCache;-><init>()V
HSPLandroidx/lifecycle/ClassesInfoCache;->createInfo(Ljava/lang/Class;[Ljava/lang/reflect/Method;)Landroidx/lifecycle/ClassesInfoCache$CallbackInfo;
HSPLandroidx/lifecycle/ClassesInfoCache;->getDeclaredMethods(Ljava/lang/Class;)[Ljava/lang/reflect/Method;
HSPLandroidx/lifecycle/ClassesInfoCache;->getInfo(Ljava/lang/Class;)Landroidx/lifecycle/ClassesInfoCache$CallbackInfo;
HSPLandroidx/lifecycle/ClassesInfoCache;->hasLifecycleMethods(Ljava/lang/Class;)Z
HSPLandroidx/lifecycle/ClassesInfoCache;->verifyAndPutHandler(Ljava/util/Map;Landroidx/lifecycle/ClassesInfoCache$MethodReference;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Class;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onCreate(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onResume(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onStart(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter$1;-><clinit>()V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter;-><init>(Landroidx/lifecycle/FullLifecycleObserver;Landroidx/lifecycle/LifecycleEventObserver;)V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/Lifecycle$1;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$Event;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$Event;-><init>(Ljava/lang/String;I)V
HSPLandroidx/lifecycle/Lifecycle$Event;->getTargetState()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/Lifecycle$Event;->upFrom(Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/Lifecycle$Event;->values()[Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/Lifecycle$State;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$State;-><init>(Ljava/lang/String;I)V
HSPLandroidx/lifecycle/Lifecycle$State;->isAtLeast(Landroidx/lifecycle/Lifecycle$State;)Z
HSPLandroidx/lifecycle/Lifecycle$State;->values()[Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/Lifecycle;-><init>()V
HSPLandroidx/lifecycle/Lifecycling;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycling;->generatedConstructor(Ljava/lang/Class;)Ljava/lang/reflect/Constructor;
HSPLandroidx/lifecycle/Lifecycling;->getAdapterName(Ljava/lang/String;)Ljava/lang/String;
HSPLandroidx/lifecycle/Lifecycling;->getObserverConstructorType(Ljava/lang/Class;)I
HSPLandroidx/lifecycle/Lifecycling;->lifecycleEventObserver(Ljava/lang/Object;)Landroidx/lifecycle/LifecycleEventObserver;
HSPLandroidx/lifecycle/Lifecycling;->resolveObserverCallbackType(Ljava/lang/Class;)I
HSPLandroidx/lifecycle/ReflectiveGenericLifecycleObserver;-><init>(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ReflectiveGenericLifecycleObserver;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onDestroy(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onPause(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onStop(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V

# Baseline profiles for lifecycle-process

HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;-><init>()V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;-><init>()V
HSPLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/LifecycleDispatcher;-><clinit>()V
HSPLandroidx/lifecycle/LifecycleDispatcher;->init(Landroid/content/Context;)V
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;-><init>()V
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->create(Landroid/content/Context;)Landroidx/lifecycle/LifecycleOwner;
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->create(Landroid/content/Context;)Ljava/lang/Object;
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->dependencies()Ljava/util/List;
HSPLandroidx/lifecycle/ProcessLifecycleOwner$1;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$2;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner$3;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;->onActivityPostResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;->onActivityPostStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityPreCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;-><clinit>()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;-><init>()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->activityResumed()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->activityStarted()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->attach(Landroid/content/Context;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->get()Landroidx/lifecycle/LifecycleOwner;
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->init(Landroid/content/Context;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner$1;->run()V
PLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner;->activityPaused()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->activityStopped()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->dispatchPauseIfNeeded()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->dispatchStopIfNeeded()V

# Baseline profile rules for androidx.compose.ui
# =============================================
#

#
# root level things
HSPLandroidx/compose/ui/Alignment**->**(**)**
HSPLandroidx/compose/ui/BiasAlignment**->**(**)**
HSPLandroidx/compose/ui/Modifier**->**(**)**
HSPLandroidx/compose/ui/CombinedModifier**->**(**)**
HSPLandroidx/compose/ui/ComposedModifier**->**(**)**
HSPLandroidx/compose/ui/KeyedComposedModifier**->**(**)**
HSPLandroidx/compose/ui/MotionDurationScale**->**(**)**
#
# autofill
HSPLandroidx/compose/ui/autofill/AndroidAutofill**->**(**)**
HSPLandroidx/compose/ui/autofill/AutofillCallback;->**(**)**
HSPLandroidx/compose/ui/autofill/AutofillTree;->**(**)**
#
# draw
HSPLandroidx/compose/ui/draw/ClipKt**->**(**)**
HSPLandroidx/compose/ui/draw/DrawBackgroundModifier;->**(**)**
HSPLandroidx/compose/ui/draw/DrawBehindElement;->**(**)**
HSPLandroidx/compose/ui/draw/DrawResult;->**(**)**
HSPLandroidx/compose/ui/draw/DrawModifier**->**(**)**
HSPLandroidx/compose/ui/draw/ShadowKt**->**(**)**
#
# focus
HSPLandroidx/compose/ui/focus/FocusChangedModifier**->**(**)**
HSPLandroidx/compose/ui/focus/FocusDirection;->**(**)**
HSPLandroidx/compose/ui/focus/FocusEventModifierKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusEventModifierLocal;->**(**)**
HSPLandroidx/compose/ui/focus/FocusInvalidationManager;->**(**)**
HSPLandroidx/compose/ui/focus/FocusManagerImpl;->**(**)**
HSPLandroidx/compose/ui/focus/FocusManagerKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusModifier**->**(**)**
HSPLandroidx/compose/ui/focus/FocusOwnerImpl**->**(**)**
HSPLandroidx/compose/ui/focus/FocusProperties**->**(**)**
HSPLandroidx/compose/ui/focus/FocusRequester**->**(**)**
HSPLandroidx/compose/ui/focus/FocusStateImpl;->**(**)**
HSPLandroidx/compose/ui/focus/FocusTargetNode**->**(**)**
HSPLandroidx/compose/ui/focus/FocusTransactionManager;->**(**)**

#
# geometry include everything
HSPLandroidx/compose/ui/geometry/**->**(**)**

#
# graphics include everything
HSPLandroidx/compose/ui/graphics/**->**(**)**

#
# spatial indexing include everything
HSPLandroidx/compose/ui/spatial/**->**(**)**

# input
HSPLandroidx/compose/ui/input/InputMode;->**(**)**
HSPLandroidx/compose/ui/input/InputModeManagerImpl;->**(**)**
HSPLandroidx/compose/ui/input/key/KeyInputElement**->**(**)**

# nested scroll
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollDelegatingWrapper;->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollDispatcher**->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollNode**->**(**)**
#
# pointer input
HSPLandroidx/compose/ui/input/pointer/AwaitPointerEventScope**->**(**)**
HSPLandroidx/compose/ui/input/pointer/ConsumedData;->**(**)**
HSPLandroidx/compose/ui/input/pointer/HistoricalChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/HitPathTracker;->**(**)**
HSPLandroidx/compose/ui/input/pointer/InternalPointerEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/MotionEventAdapter;->**(**)**
HSPLandroidx/compose/ui/input/pointer/MotionEventAdapter_androidKt;->**(**)**
HSPLandroidx/compose/ui/input/pointer/Node;->**(**)**
HSPLandroidx/compose/ui/input/pointer/NodeParent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEventKt;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEventPass;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerId;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChangeEventProducer**->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEventData;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEventProcessor;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputFilter;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInteropFilter**->**(**)**
HSPLandroidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/ProcessResult;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerType;->**(**)**
HSPLandroidx/compose/ui/input/pointer/SuspendingPointerInputFilter**->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/ImpulseCalculator;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/Matrix;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PointAtTime;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PointerIdArray;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PolynomialFit;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityEstimate;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityTracker**->**(**)**

#
# rotary
HSPLandroidx/compose/ui/input/rotary/RotaryInputModifier**->**(**)**

#
# layout. include everything
HSPLandroidx/compose/ui/layout/**->**(**)**
#
# modifier. include everything
HSPLandroidx/compose/ui/modifier/**->**(**)**
#
# node. include everything
HSPLandroidx/compose/ui/node/**->**(**)**
#
# platform
HSPLandroidx/compose/ui/platform/AndroidComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/AbstractComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewForceDarkMode**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethods**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeView_androidKt;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidCompositionLocals_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidFontResourceLoader;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidTextToolbar;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiDispatcher**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiFrameClock**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUriHandler;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidViewConfiguration;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidViewsHandler;->**(**)**
HSPLandroidx/compose/ui/platform/ComposableSingletons**->**(**)**
HSPLandroidx/compose/ui/platform/ComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/CompositionLocalsKt**->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry;->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/GlobalSnapshotManager**->**(**)**
HSPLandroidx/compose/ui/platform/InspectableModifier**->**(**)**
HSPLandroidx/compose/ui/platform/InspectableValueKt**->**(**)**
HSPLandroidx/compose/ui/platform/InspectorValueInfo;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLandroidx/compose/ui/platform/InvertMatrixKt;->**(**)**
HSPLandroidx/compose/ui/platform/LayerMatrixCache;->**(**)**
HSPLandroidx/compose/ui/platform/MotionDurationScaleImpl;->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeLayer**->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeApi**->**(**)**
HSPLandroidx/compose/ui/platform/OutlineResolver;->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeMatrixCache;->**(**)**
HSPLandroidx/compose/ui/platform/ViewCompositionStrategy**->**(**)**
HSPLandroidx/compose/ui/platform/ViewLayer;->**(**)**
HSPLandroidx/compose/ui/platform/WeakCache;->**(**)**
HSPLandroidx/compose/ui/platform/WindowInfoImpl;->**(**)**
HSPLandroidx/compose/ui/platform/WindowRecomposerPolicy**->**(**)**
HSPLandroidx/compose/ui/platform/WindowRecomposer_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/WrappedComposition**->**(**)**
HSPLandroidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods**->**(**)**
HSPLandroidx/compose/ui/platform/Wrapper**->**(**)**
HSPLandroidx/compose/ui/platform/accessibility/CollectionInfoKt;->**(**)**
#
# semantics
HSPLandroidx/compose/ui/semantics/AccessibilityAction;->**(**)**
HSPLandroidx/compose/ui/semantics/AppendedSemanticsElement;->**(**)**
HSPLandroidx/compose/ui/semantics/CollectionInfo;->**(**)**
HSPLandroidx/compose/ui/semantics/CoreSemanticsModifierNode;->**(**)**
HSPLandroidx/compose/ui/semantics/EmptySemanticsElement;->**(**)**
HSPLandroidx/compose/ui/semantics/NodeLocationHolder;->**(**)**
HSPLandroidx/compose/ui/semantics/Role;->**(**)**
HSPLandroidx/compose/ui/semantics/ScrollAxisRange;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsActions;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsConfiguration;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsEntity;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifier$DefaultImpls;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierCore$Companion;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierCore;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNode;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNode$parent$1;->**(**)**
HSPLandroidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNodeKt;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsOwner;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsProperties**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsPropertiesKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsPropertyKey**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsSort**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsWrapper;->**(**)**
#
# res
HSPLandroidx/compose/ui/res/ImageVectorCache;->**(**)**
HSPLandroidx/compose/ui/res/StringResources_androidKt;->**(**)**
HSPLandroidx/compose/ui/res/PainterResources_androidKt;->**(**)**
HSPLandroidx/compose/ui/res/ImageResources_androidKt;->**(**)**

# Baseline profile rules for androidx.compose.runtime.saveable
# =============================================
HSPLandroidx/compose/runtime/saveable/RememberSaveableKt**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateHolderImpl**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateHolderKt**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateRegistryKt;->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateRegistryImpl;->**(**)**
Landroidx/compose/runtime/saveable/*;
# Baseline profile rules for androidx.compose.runtime
# =============================================
#
# We prioritize everything at the top level, and a few sub-namespaces
Landroidx/compose/runtime/*;
Landroidx/compose/runtime/snapshots/*;
Landroidx/compose/runtime/internal/*;
Landroidx/compose/runtime/external/kotlinx/collections/immutable/**;
#
# Core runtime classes
# ====
# Note: AbstractApplier might benefit from inline caches. consider removing.
HSPLandroidx/compose/runtime/AbstractApplier;->**(**)**
HSPLandroidx/compose/runtime/ActualJvm_jvmKt;->identityHashCode(Ljava/lang/Object;)I
HSPLandroidx/compose/runtime/ActualAndroid**->**(**)**
HSPLandroidx/compose/runtime/Anchor;->**(**)**
HSPLandroidx/compose/runtime/Applier$DefaultImpls;->**(**)**
HSPLandroidx/compose/runtime/BroadcastFrameClock**->**(**)**
HSPLandroidx/compose/runtime/ComposablesKt;->**(**)**
HSPLandroidx/compose/runtime/ComposableSingletons**->**(**)**
HSPLandroidx/compose/runtime/ComposerImpl**->**(**)**
HSPLandroidx/compose/runtime/ComposerKt**->**(**)**
HSPLandroidx/compose/runtime/CompositionContext;->**(**)**
HSPLandroidx/compose/runtime/CompositionImpl**->**(**)**
HSPLandroidx/compose/runtime/CompositionKt;->**(**)**
HSPLandroidx/compose/runtime/CompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/CompositionLocalKt;->**(**)**
HSPLandroidx/compose/runtime/CompositionScopedCoroutineScopeCanceller;->**(**)**
HSPLandroidx/compose/runtime/DerivedSnapshotState**->**(**)**
HSPLandroidx/compose/runtime/DisposableEffectImpl;->**(**)**
HSPLandroidx/compose/runtime/DisposableEffectScope;->**(**)**
HSPLandroidx/compose/runtime/DynamicProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/EffectsKt;->**(**)**
HSPLandroidx/compose/runtime/GroupInfo;->**(**)**
HSPLandroidx/compose/runtime/GroupKind**->**(**)**
HSPLandroidx/compose/runtime/InvalidationResult;->**(**)**
HSPLandroidx/compose/runtime/Invalidation;->**(**)**
HSPLandroidx/compose/runtime/KeyInfo;->**(**)**
HSPLandroidx/compose/runtime/Latch**->**(**)**
HSPLandroidx/compose/runtime/LaunchedEffectImpl;->**(**)**
HSPLandroidx/compose/runtime/LazyValueHolder;->**(**)**
HSPLandroidx/compose/runtime/MonotonicFrameClock**->**(**)**
HSPLandroidx/compose/runtime/NeverEqualPolicy;->**(**)**
HSPLandroidx/compose/runtime/OpaqueKey;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableFloatState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableIntState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableLongState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableState**->**(**)**
HSPLandroidx/compose/runtime/PausableMonotonicFrameClock;->**(**)**
HSPLandroidx/compose/runtime/Pending**->**(**)**
HSPLandroidx/compose/runtime/ProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/ProvidedValue;->**(**)**
HSPLandroidx/compose/runtime/RecomposeScopeImpl;->**(**)**
HSPLandroidx/compose/runtime/Recomposer**->**(**)**
HSPLandroidx/compose/runtime/ReferentialEqualityPolicy;->**(**)**
HSPLandroidx/compose/runtime/SkippableUpdater;->**(**)**
HSPLandroidx/compose/runtime/SlotReader;->**(**)**
HSPLandroidx/compose/runtime/SlotTable;->**(**)**
HSPLandroidx/compose/runtime/SlotTableKt;->**(**)**
HSPLandroidx/compose/runtime/SlotWriter**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableFloatStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableIntStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableLongStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotDoubleStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotIntStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotLongStateKt**->**(**)**
HSPLandroidx/compose/runtime/PrimitiveSnapshotStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotThreadLocal;->**(**)**
HSPLandroidx/compose/runtime/StaticProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/StaticValueHolder;->**(**)**
HSPLandroidx/compose/runtime/StructuralEqualityPolicy;->**(**)**
HSPLandroidx/compose/runtime/Trace;->**(**)**
HSPLandroidx/compose/runtime/Updater**->**(**)**
HSPLandroidx/compose/runtime/changelist/**->**(**)**
HSPLandroidx/compose/runtime/internal/ComposableLambdaImpl**->**(**)**
HSPLandroidx/compose/runtime/internal/ComposableLambdaKt;->**(**)**
HSPLandroidx/compose/runtime/internal/IntRef;->**(**)**
HSPLandroidx/compose/runtime/tooling/**->**(**)**
HSPLandroidx/compose/runtime/tracing/**->**(**)**

#
# Snapshot related stuff
HSPLandroidx/compose/runtime/snapshots/MutableSnapshot;->**(**)**
HSPLandroidx/compose/runtime/snapshots/NestedMutableSnapshot;->**(**)**
HSPLandroidx/compose/runtime/snapshots/Snapshot**->**(**)**
HSPLandroidx/compose/runtime/snapshots/ListUtilsKt;->fastToSet(Ljava/util/List;)Ljava/util/Set;
HSPLandroidx/compose/runtime/snapshots/SnapshotApplyResult**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotIdSet**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotStateList**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotStateObserver**->**(**)**
HSPLandroidx/compose/runtime/snapshots/StateRecord;->**(**)**
HSPLandroidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot;->**(**)**
#
# MutableVector and other purpose-built data structures are hot paths
HSPLandroidx/compose/runtime/collection/**->**(**)**
HSPLandroidx/compose/runtime/Stack;->**(**)**
HSPLandroidx/compose/runtime/IntStack;->**(**)**
HSPLandroidx/compose/runtime/internal/PersistentCompositionLocalHashMap**->**(**)**
HSPLandroidx/compose/runtime/internal/ThreadMap;->**(**)**
HSPLandroidx/compose/runtime/PrioritySet;->**(**)**
#
# AndroidX collections
Landroidx/collection/**;
HSPLandroidx/collection/ArraySet**->**(**)**
HSPLandroidx/collection/IntSetKt;->**(**)**
HSPLandroidx/collection/LongSparseArray**->**(**)**
HSPLandroidx/collection/MutableIntIntMap;->**(**)**
HSPLandroidx/collection/MutableObjectIntMap;->**(**)**
HSPLandroidx/collection/MutableScatterMap;->**(**)**
HSPLandroidx/collection/MutableScatterSet**->**(**)**
HSPLandroidx/collection/ObjectIntMapKt;->**(**)**
HSPLandroidx/collection/ScatterMapKt;->**(**)**
HSPLandroidx/collection/ScatterSet**->**(**)**
HSPLandroidx/collection/SimpleArrayMap;->**(**)**
HSPLandroidx/collection/SparseArrayCompat;->**(**)**
HSPLandroidx/collection/internal/ContainerHelpersKt;->**(**)**
#
# kotlinx.collections.immutable copy
# ====
# We only use a subset of these methods but haven't gotten rid of all of the APIs to preserve
# source. Since this is very niche usage, this should stay pretty consistent.
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentHashMapOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentListOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentSetOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;->getEMPTY()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;-><init>([Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->add(Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->addAll(Ljava/util/Collection;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->get(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt;->persistentVectorOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;->getKey()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;->getValue()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;->emptyOf$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->builder()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->builder()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->containsKey(Ljava/lang/Object;)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->createEntries()Landroidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getEntries()Ljava/util/Set;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getNode$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;[Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->checkHasNext()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->ensureNextEntryIsReady()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->hasNext()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->moveToNextNodeWithData(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->next()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->build()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->build()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getModCount$runtime_release()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getOwnership$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->putAll(Ljava/util/Map;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setModCount$runtime_release(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setOperationResult$runtime_release(Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setSize(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;->iterator()Ljava/util/Iterator;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;->getEMPTY$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;->getNode()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;->getSizeDelta()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;-><init>(II[Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;-><init>(II[Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->asInsertResult()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->containsKey(ILjava/lang/Object;I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->elementsIdentityEquals(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->entryCount$runtime_release()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->entryKeyIndex$runtime_release(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->get(ILjava/lang/Object;I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->getBuffer$runtime_release()[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->hasEntryAt$runtime_release(I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->hasNodeAt(I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->insertEntryAt(ILjava/lang/Object;Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->keyAtIndex(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->makeNode(ILjava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutableInsertEntryAt(ILjava/lang/Object;Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePut(ILjava/lang/Object;Ljava/lang/Object;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePutAll(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePutAllFromOtherNodeCell(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;IILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutableUpdateValueAtIndex(ILjava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->nodeAtIndex$runtime_release(I)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->nodeIndex$runtime_release(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->put(ILjava/lang/Object;Ljava/lang/Object;I)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->valueAtKeyIndex(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->getBuffer()[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->getIndex()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->hasNextKey()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->hasNextNode()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->reset([Ljava/lang/Object;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->reset([Ljava/lang/Object;II)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->setIndex(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;->next()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;->next()Ljava/util/Map$Entry;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->access$insertEntryAtIndex([Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->indexSegment(II)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->insertEntryAtIndex([Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;->emptyOf$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;-><init>(Ljava/lang/Object;Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->add(Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt;->assert(Z)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;-><init>(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;->getCount()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;->setCount(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation;->checkElementIndex$runtime_release(II)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;-><init>()V
#
# important external / stdlib methods and classes
# Since compose heavily relies on various kotlin standard libraries, it is important that these get
# compiled as well. Since the std libraries are large and we don't use everything, we are
# conservative here and avoid wildcards and instead use profile dumps to guide us
HSPLkotlin/ULong$Companion;-><init>()V
HSPLkotlin/ULong$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/ULong;->constructor-impl(J)J
HSPLkotlin/UnsignedKt;->ulongToDouble(J)D
HSPLkotlin/collections/AbstractCollection;-><init>()V
HSPLkotlin/collections/AbstractCollection;->isEmpty()Z
HSPLkotlin/collections/AbstractCollection;->size()I
HSPLkotlin/collections/AbstractList$Companion;-><init>()V
HSPLkotlin/collections/AbstractList$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractList$IteratorImpl;-><init>(Lkotlin/collections/AbstractList;)V
HSPLkotlin/collections/AbstractList$IteratorImpl;->hasNext()Z
HSPLkotlin/collections/AbstractList$IteratorImpl;->next()Ljava/lang/Object;
HSPLkotlin/collections/AbstractList;-><init>()V
HSPLkotlin/collections/AbstractList;->iterator()Ljava/util/Iterator;
HSPLkotlin/collections/AbstractMap$Companion;-><init>()V
HSPLkotlin/collections/AbstractMap$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractMap;-><init>()V
HSPLkotlin/collections/AbstractMap;->containsEntry$kotlin_stdlib(Ljava/util/Map$Entry;)Z
HSPLkotlin/collections/AbstractMap;->entrySet()Ljava/util/Set;
HSPLkotlin/collections/AbstractMap;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/AbstractMap;->size()I
HSPLkotlin/collections/AbstractMutableList;-><init>()V
HSPLkotlin/collections/AbstractMutableList;->size()I
HSPLkotlin/collections/AbstractMutableMap;-><init>()V
HSPLkotlin/collections/AbstractMutableMap;->size()I
HSPLkotlin/collections/AbstractSet$Companion;-><init>()V
HSPLkotlin/collections/AbstractSet$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractSet$Companion;->setEquals$kotlin_stdlib(Ljava/util/Set;Ljava/util/Set;)Z
HSPLkotlin/collections/AbstractSet;-><init>()V
HSPLkotlin/collections/AbstractSet;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/ArrayAsCollection;-><init>([Ljava/lang/Object;Z)V
HSPLkotlin/collections/ArrayAsCollection;->toArray()[Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque$Companion;-><init>()V
HSPLkotlin/collections/ArrayDeque$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/ArrayDeque$Companion;->newCapacity$kotlin_stdlib(II)I
HSPLkotlin/collections/ArrayDeque;-><init>()V
HSPLkotlin/collections/ArrayDeque;->access$getElementData$p(Lkotlin/collections/ArrayDeque;)[Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque;->access$getHead$p(Lkotlin/collections/ArrayDeque;)I
HSPLkotlin/collections/ArrayDeque;->access$positiveMod(Lkotlin/collections/ArrayDeque;I)I
HSPLkotlin/collections/ArrayDeque;->addLast(Ljava/lang/Object;)V
HSPLkotlin/collections/ArrayDeque;->copyElements(I)V
HSPLkotlin/collections/ArrayDeque;->ensureCapacity(I)V
HSPLkotlin/collections/ArrayDeque;->getSize()I
HSPLkotlin/collections/ArrayDeque;->incremented(I)I
HSPLkotlin/collections/ArrayDeque;->isEmpty()Z
HSPLkotlin/collections/ArrayDeque;->positiveMod(I)I
HSPLkotlin/collections/ArrayDeque;->removeFirst()Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque;->removeFirstOrNull()Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt__ArraysJVMKt;->copyOfRangeToIndexCheck(II)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;-><init>([F)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->get(I)Ljava/lang/Float;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->get(I)Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->getSize()I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->asList([F)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->asList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([F[FIIIILjava/lang/Object;)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([I[IIIIILjava/lang/Object;)[I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([Ljava/lang/Object;[Ljava/lang/Object;IIIILjava/lang/Object;)[Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([F[FIII)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([I[IIII)[I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([Ljava/lang/Object;[Ljava/lang/Object;III)[Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyOfRange([FII)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill$default([IIIIILjava/lang/Object;)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill([IIII)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill([Ljava/lang/Object;Ljava/lang/Object;II)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->sort([Ljava/lang/Object;)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->sortWith([Ljava/lang/Object;Ljava/util/Comparator;II)V
HSPLkotlin/collections/ArraysKt___ArraysKt;->contains([CC)Z
HSPLkotlin/collections/ArraysKt___ArraysKt;->first([Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysKt;->getLastIndex([Ljava/lang/Object;)I
HSPLkotlin/collections/ArraysKt___ArraysKt;->indexOf([CC)I
HSPLkotlin/collections/ArraysKt___ArraysKt;->slice([FLkotlin/ranges/IntRange;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysKt;->toList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysKt;->toMutableList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysUtilJVM;->asList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsJVMKt;->copyToArrayOfAny([Ljava/lang/Object;Z)[Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt__CollectionsJVMKt;->listOf(Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->arrayListOf([Ljava/lang/Object;)Ljava/util/ArrayList;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->asCollection([Ljava/lang/Object;)Ljava/util/Collection;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->emptyList()Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->getLastIndex(Ljava/util/List;)I
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->listOf([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__IterablesKt;->collectionSizeOrDefault(Ljava/lang/Iterable;I)I
HSPLkotlin/collections/CollectionsKt__MutableCollectionsJVMKt;->sortWith(Ljava/util/List;Ljava/util/Comparator;)V
HSPLkotlin/collections/CollectionsKt__MutableCollectionsKt;->addAll(Ljava/util/Collection;Ljava/lang/Iterable;)Z
HSPLkotlin/collections/CollectionsKt__MutableCollectionsKt;->removeFirstOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->distinct(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->filterNotNull(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->filterNotNullTo(Ljava/lang/Iterable;Ljava/util/Collection;)Ljava/util/Collection;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->first(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->getOrNull(Ljava/util/List;I)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->last(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->lastOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->maxOrNull(Ljava/lang/Iterable;)Ljava/lang/Float;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->minOrNull(Ljava/lang/Iterable;)Ljava/lang/Float;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->plus(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toFloatArray(Ljava/util/Collection;)[F
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toIntArray(Ljava/util/Collection;)[I
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toList(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toMutableList(Ljava/util/Collection;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toMutableSet(Ljava/lang/Iterable;)Ljava/util/Set;
HSPLkotlin/collections/EmptyList;-><init>()V
HSPLkotlin/collections/EmptyList;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyList;->getSize()I
HSPLkotlin/collections/EmptyList;->isEmpty()Z
HSPLkotlin/collections/EmptyList;->size()I
HSPLkotlin/collections/EmptyList;->toArray()[Ljava/lang/Object;
HSPLkotlin/collections/EmptyMap;-><init>()V
HSPLkotlin/collections/EmptyMap;->containsKey(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyMap;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyMap;->get(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/EmptyMap;->get(Ljava/lang/Object;)Ljava/lang/Void;
HSPLkotlin/collections/EmptyMap;->isEmpty()Z
HSPLkotlin/collections/IntIterator;-><init>()V
HSPLkotlin/collections/MapsKt__MapWithDefaultKt;->getOrImplicitDefaultNullable(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/MapsKt__MapsJVMKt;->mapCapacity(I)I
HSPLkotlin/collections/MapsKt__MapsKt;->emptyMap()Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->getValue(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/MapsKt__MapsKt;->mapOf([Lkotlin/Pair;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->putAll(Ljava/util/Map;Ljava/lang/Iterable;)V
HSPLkotlin/collections/MapsKt__MapsKt;->putAll(Ljava/util/Map;[Lkotlin/Pair;)V
HSPLkotlin/collections/MapsKt__MapsKt;->toMap(Ljava/lang/Iterable;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMap(Ljava/lang/Iterable;Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMap([Lkotlin/Pair;Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMutableMap(Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/comparisons/ComparisonsKt__ComparisonsKt;->compareValues(Ljava/lang/Comparable;Ljava/lang/Comparable;)I
HSPLkotlin/jvm/internal/CollectionToArray;->toArray(Ljava/util/Collection;)[Ljava/lang/Object;
HSPLkotlin/jvm/internal/FloatCompanionObject;-><init>()V
HSPLkotlin/jvm/internal/FunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/FunctionReference;->equals(Ljava/lang/Object;)Z
HSPLkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/InlineMarker;->mark(I)V
HSPLkotlin/jvm/internal/IntCompanionObject;-><init>()V
HSPLkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlin/jvm/internal/Intrinsics;->checkExpressionValueIsNotNull(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkParameterIsNotNull(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->compare(II)I
HSPLkotlin/jvm/internal/Lambda;-><init>(I)V
HSPLkotlin/jvm/internal/Lambda;->getArity()I
HSPLkotlin/math/MathKt__MathJVMKt;->getSign(I)I
HSPLkotlin/math/MathKt__MathJVMKt;->roundToInt(F)I
HSPLkotlin/ranges/IntRange$Companion;-><init>()V
HSPLkotlin/ranges/IntRange$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/ranges/IntRange;-><init>(II)V
HSPLkotlin/ranges/IntRange;->getEndInclusive()Ljava/lang/Integer;
HSPLkotlin/ranges/IntRange;->getStart()Ljava/lang/Integer;
HSPLkotlin/ranges/IntRange;->isEmpty()Z
HSPLkotlin/ranges/RangesKt__RangesKt;->checkStepIsPositive(ZLjava/lang/Number;)V
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(II)I
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(JJ)J
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(Ljava/lang/Comparable;Ljava/lang/Comparable;)Ljava/lang/Comparable;
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtMost(II)I
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtMost(JJ)J
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(DDD)D
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(FFF)F
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(III)I
HSPLkotlin/ranges/RangesKt___RangesKt;->step(Lkotlin/ranges/IntProgression;I)Lkotlin/ranges/IntProgression;
HSPLkotlin/ranges/RangesKt___RangesKt;->until(II)Lkotlin/ranges/IntRange;
HSPLkotlinx/coroutines/AbstractCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Z)V
HSPLkotlinx/coroutines/AbstractCoroutine;->afterResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->cancellationExceptionMessage()Ljava/lang/String;
HSPLkotlinx/coroutines/AbstractCoroutine;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/AbstractCoroutine;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/AbstractCoroutine;->initParentJob$kotlinx_coroutines_core()V
HSPLkotlinx/coroutines/AbstractCoroutine;->isActive()Z
HSPLkotlinx/coroutines/AbstractCoroutine;->onCancelled(Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/AbstractCoroutine;->onCompleted(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->onCompletionInternal(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->resumeWith(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->start(Lkotlinx/coroutines/CoroutineStart;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V
HSPLkotlinx/coroutines/Active;-><init>()V
HSPLkotlinx/coroutines/BeforeResumeCancelHandler;-><init>()V
HSPLkotlinx/coroutines/BlockingEventLoop;-><init>(Ljava/lang/Thread;)V
HSPLkotlinx/coroutines/BuildersKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt;->launch(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt;->withContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->launch(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->withContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancelHandler;-><init>()V
HSPLkotlinx/coroutines/CancelHandlerBase;-><init>()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;-><init>(Lkotlin/coroutines/Continuation;I)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->callCancelHandler(Lkotlinx/coroutines/CancelHandler;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancel(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancelCompletedResult$kotlinx_coroutines_core(Ljava/lang/Object;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancelLater(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->checkCompleted()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->completeResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->detachChild$kotlinx_coroutines_core()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->detachChildIfNonResuable()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->dispatchResume(I)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getContinuationCancellationCause(Lkotlinx/coroutines/Job;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getDelegate$kotlinx_coroutines_core()Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getExceptionalResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getParentHandle()Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getSuccessfulResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->initCancellability()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->invokeOnCancellation(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->isCompleted()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->isReusable()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->makeCancelHandler(Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/CancelHandler;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->parentCancelled$kotlinx_coroutines_core(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resetStateReusable()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeImpl$default(Lkotlinx/coroutines/CancellableContinuationImpl;Ljava/lang/Object;ILkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeImpl(Ljava/lang/Object;ILkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeUndispatched(Lkotlinx/coroutines/CoroutineDispatcher;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeWith(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumedState(Lkotlinx/coroutines/NotCompleted;Ljava/lang/Object;ILkotlin/jvm/functions/Function1;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->setParentHandle(Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->setupCancellation()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->takeState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResume()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResume(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResumeImpl(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->trySuspend()Z
HSPLkotlinx/coroutines/CancellableContinuationKt;->disposeOnCancellation(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/CancellableContinuationKt;->getOrCreateCancellableContinuation(Lkotlin/coroutines/Continuation;)Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/CancellableContinuationKt;->removeOnCancellation(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/CancelledContinuation;-><init>(Lkotlin/coroutines/Continuation;Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/CancelledContinuation;->makeResumed()Z
HSPLkotlinx/coroutines/ChildContinuation;-><init>(Lkotlinx/coroutines/CancellableContinuationImpl;)V
HSPLkotlinx/coroutines/ChildContinuation;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/ChildHandleNode;-><init>(Lkotlinx/coroutines/ChildJob;)V
HSPLkotlinx/coroutines/ChildHandleNode;->childCancelled(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/ChildHandleNode;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedContinuation;-><init>(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedContinuation;-><init>(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CompletedContinuation;->copy$default(Lkotlinx/coroutines/CompletedContinuation;Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;ILjava/lang/Object;)Lkotlinx/coroutines/CompletedContinuation;
HSPLkotlinx/coroutines/CompletedContinuation;->copy(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;)Lkotlinx/coroutines/CompletedContinuation;
HSPLkotlinx/coroutines/CompletedContinuation;->getCancelled()Z
HSPLkotlinx/coroutines/CompletedContinuation;->invokeHandlers(Lkotlinx/coroutines/CancellableContinuationImpl;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedExceptionally;-><init>(Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/CompletedExceptionally;-><init>(Ljava/lang/Throwable;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CompletedExceptionally;->getHandled()Z
HSPLkotlinx/coroutines/CompletedExceptionally;->makeHandled()Z
HSPLkotlinx/coroutines/CompletionHandlerBase;-><init>()V
HSPLkotlinx/coroutines/CompletionStateKt;->recoverResult(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState$default(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState(Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CoroutineContextKt;->createDefaultDispatcher()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/CoroutineContextKt;->newCoroutineContext(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CoroutineDispatcher$Key$1;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher$Key;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher$Key;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/CoroutineDispatcher;->interceptContinuation(Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/CoroutineDispatcher;->isDispatchNeeded(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/CoroutineDispatcher;->minusKey(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CoroutineDispatcher;->releaseInterceptedContinuation(Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/CoroutineExceptionHandler$Key;-><init>()V
HSPLkotlinx/coroutines/CoroutineScopeKt;->CoroutineScope(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineScope;
HSPLkotlinx/coroutines/CoroutineScopeKt;->cancel$default(Lkotlinx/coroutines/CoroutineScope;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/CoroutineScopeKt;->cancel(Lkotlinx/coroutines/CoroutineScope;Ljava/util/concurrent/CancellationException;)V
HSPLkotlinx/coroutines/CoroutineScopeKt;->coroutineScope(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CoroutineScopeKt;->isActive(Lkotlinx/coroutines/CoroutineScope;)Z
HSPLkotlinx/coroutines/CoroutineStart;-><init>(Ljava/lang/String;I)V
HSPLkotlinx/coroutines/CoroutineStart;->invoke(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/CoroutineStart;->isLazy()Z
HSPLkotlinx/coroutines/CoroutineStart;->values()[Lkotlinx/coroutines/CoroutineStart;
HSPLkotlinx/coroutines/DebugKt;->getASSERTIONS_ENABLED()Z
HSPLkotlinx/coroutines/DebugKt;->getDEBUG()Z
HSPLkotlinx/coroutines/DebugKt;->getRECOVER_STACK_TRACES()Z
HSPLkotlinx/coroutines/DebugStringsKt;->getClassSimpleName(Ljava/lang/Object;)Ljava/lang/String;
HSPLkotlinx/coroutines/DefaultExecutor;-><init>()V
HSPLkotlinx/coroutines/DefaultExecutor;->createThreadSync()Ljava/lang/Thread;
HSPLkotlinx/coroutines/DefaultExecutor;->getThread()Ljava/lang/Thread;
HSPLkotlinx/coroutines/DefaultExecutor;->isShutdownRequested()Z
HSPLkotlinx/coroutines/DefaultExecutor;->notifyStartup()Z
HSPLkotlinx/coroutines/DefaultExecutor;->run()V
HSPLkotlinx/coroutines/DefaultExecutorKt;->getDefaultDelay()Lkotlinx/coroutines/Delay;
HSPLkotlinx/coroutines/DelayKt;->delay(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/DelayKt;->getDelay(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Delay;
HSPLkotlinx/coroutines/DispatchedTask;-><init>(I)V
HSPLkotlinx/coroutines/DispatchedTask;->getExceptionalResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/DispatchedTask;->getSuccessfulResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/DispatchedTask;->handleFatalException(Ljava/lang/Throwable;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/DispatchedTask;->run()V
HSPLkotlinx/coroutines/DispatchedTaskKt;->dispatch(Lkotlinx/coroutines/DispatchedTask;I)V
HSPLkotlinx/coroutines/DispatchedTaskKt;->isCancellableMode(I)Z
HSPLkotlinx/coroutines/DispatchedTaskKt;->isReusableMode(I)Z
HSPLkotlinx/coroutines/DispatchedTaskKt;->resume(Lkotlinx/coroutines/DispatchedTask;Lkotlin/coroutines/Continuation;Z)V
HSPLkotlinx/coroutines/DispatchedTaskKt;->resumeUnconfined(Lkotlinx/coroutines/DispatchedTask;)V
HSPLkotlinx/coroutines/Dispatchers;-><init>()V
HSPLkotlinx/coroutines/Dispatchers;->getDefault()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/DisposeOnCancel;-><init>(Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/Empty;-><init>(Z)V
HSPLkotlinx/coroutines/Empty;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/Empty;->isActive()Z
HSPLkotlinx/coroutines/EventLoop;-><init>()V
HSPLkotlinx/coroutines/EventLoop;->decrementUseCount(Z)V
HSPLkotlinx/coroutines/EventLoop;->delta(Z)J
HSPLkotlinx/coroutines/EventLoop;->getNextTime()J
HSPLkotlinx/coroutines/EventLoop;->incrementUseCount$default(Lkotlinx/coroutines/EventLoop;ZILjava/lang/Object;)V
HSPLkotlinx/coroutines/EventLoop;->incrementUseCount(Z)V
HSPLkotlinx/coroutines/EventLoop;->isUnconfinedLoopActive()Z
HSPLkotlinx/coroutines/EventLoop;->processUnconfinedEvent()Z
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedResumeTask;-><init>(Lkotlinx/coroutines/EventLoopImplBase;JLkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedResumeTask;->run()V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;-><init>(J)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->scheduleTask(JLkotlinx/coroutines/EventLoopImplBase$DelayedTaskQueue;Lkotlinx/coroutines/EventLoopImplBase;)I
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->setHeap(Lkotlinx/coroutines/internal/ThreadSafeHeap;)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->setIndex(I)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->timeToExecute(J)Z
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTaskQueue;-><init>(J)V
HSPLkotlinx/coroutines/EventLoopImplBase;-><init>()V
HSPLkotlinx/coroutines/EventLoopImplBase;->access$isCompleted$p(Lkotlinx/coroutines/EventLoopImplBase;)Z
HSPLkotlinx/coroutines/EventLoopImplBase;->dequeue()Ljava/lang/Runnable;
HSPLkotlinx/coroutines/EventLoopImplBase;->enqueueImpl(Ljava/lang/Runnable;)Z
HSPLkotlinx/coroutines/EventLoopImplBase;->getNextTime()J
HSPLkotlinx/coroutines/EventLoopImplBase;->isCompleted()Z
HSPLkotlinx/coroutines/EventLoopImplBase;->processNextEvent()J
HSPLkotlinx/coroutines/EventLoopImplBase;->schedule(JLkotlinx/coroutines/EventLoopImplBase$DelayedTask;)V
HSPLkotlinx/coroutines/EventLoopImplBase;->scheduleImpl(JLkotlinx/coroutines/EventLoopImplBase$DelayedTask;)I
HSPLkotlinx/coroutines/EventLoopImplBase;->scheduleResumeAfterDelay(JLkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/EventLoopImplBase;->shouldUnpark(Lkotlinx/coroutines/EventLoopImplBase$DelayedTask;)Z
HSPLkotlinx/coroutines/EventLoopImplPlatform;-><init>()V
HSPLkotlinx/coroutines/EventLoopImplPlatform;->unpark()V
HSPLkotlinx/coroutines/EventLoopKt;->createEventLoop()Lkotlinx/coroutines/EventLoop;
HSPLkotlinx/coroutines/EventLoop_commonKt;->access$getCLOSED_EMPTY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/EventLoop_commonKt;->access$getDISPOSED_TASK$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/EventLoop_commonKt;->delayToNanos(J)J
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key$1;-><init>()V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key;-><init>()V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/GlobalScope;-><init>()V
HSPLkotlinx/coroutines/GlobalScope;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/InvokeOnCancel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/InvokeOnCancel;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/InvokeOnCompletion;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/Job$DefaultImpls;->cancel$default(Lkotlinx/coroutines/Job;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/Job$DefaultImpls;->fold(Lkotlinx/coroutines/Job;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/Job$DefaultImpls;->get(Lkotlinx/coroutines/Job;Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/Job$DefaultImpls;->invokeOnCompletion$default(Lkotlinx/coroutines/Job;ZZLkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/Job$DefaultImpls;->minusKey(Lkotlinx/coroutines/Job;Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/Job$Key;-><init>()V
HSPLkotlinx/coroutines/JobCancellationException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobCancellationException;->equals(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobCancellationException;->fillInStackTrace()Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobCancellingNode;-><init>()V
HSPLkotlinx/coroutines/JobImpl;-><init>(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobImpl;->getHandlesException$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobImpl;->getOnCancelComplete$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobImpl;->handlesException()Z
HSPLkotlinx/coroutines/JobKt;->Job$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt;->Job(Lkotlinx/coroutines/Job;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt;->ensureActive(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobKt;->getJob(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/JobKt;->isActive(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/JobKt__JobKt;->Job$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt__JobKt;->Job(Lkotlinx/coroutines/Job;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt__JobKt;->ensureActive(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobKt__JobKt;->getJob(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/JobKt__JobKt;->isActive(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/JobNode;-><init>()V
HSPLkotlinx/coroutines/JobNode;->dispose()V
HSPLkotlinx/coroutines/JobNode;->getJob()Lkotlinx/coroutines/JobSupport;
HSPLkotlinx/coroutines/JobNode;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobNode;->isActive()Z
HSPLkotlinx/coroutines/JobNode;->setJob(Lkotlinx/coroutines/JobSupport;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;-><init>(Lkotlinx/coroutines/NodeList;ZLjava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->addExceptionLocked(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->allocateList()Ljava/util/ArrayList;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getExceptionsHolder()Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getRootCause()Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport$Finishing;->isActive()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->isCancelling()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->isCompleting()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->sealLocked(Ljava/lang/Throwable;)Ljava/util/List;
HSPLkotlinx/coroutines/JobSupport$Finishing;->setCompleting(Z)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->setExceptionsHolder(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->setRootCause(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/JobSupport;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;-><init>(Z)V
HSPLkotlinx/coroutines/JobSupport;->access$cancellationExceptionMessage(Lkotlinx/coroutines/JobSupport;)Ljava/lang/String;
HSPLkotlinx/coroutines/JobSupport;->addLastAtomic(Ljava/lang/Object;Lkotlinx/coroutines/NodeList;Lkotlinx/coroutines/JobNode;)Z
HSPLkotlinx/coroutines/JobSupport;->addSuppressedExceptions(Ljava/lang/Throwable;Ljava/util/List;)V
HSPLkotlinx/coroutines/JobSupport;->afterCompletion(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->attachChild(Lkotlinx/coroutines/ChildJob;)Lkotlinx/coroutines/ChildHandle;
HSPLkotlinx/coroutines/JobSupport;->cancel(Ljava/util/concurrent/CancellationException;)V
HSPLkotlinx/coroutines/JobSupport;->cancelImpl$kotlinx_coroutines_core(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobSupport;->cancelInternal(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->cancelMakeCompleting(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->cancelParent(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->cancellationExceptionMessage()Ljava/lang/String;
HSPLkotlinx/coroutines/JobSupport;->childCancelled(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->completeStateFinalization(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->createCauseException(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport;->finalizeFinishingState(Lkotlinx/coroutines/JobSupport$Finishing;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->firstChild(Lkotlinx/coroutines/Incomplete;)Lkotlinx/coroutines/ChildHandleNode;
HSPLkotlinx/coroutines/JobSupport;->fold(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/JobSupport;->getCancellationException()Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->getChildJobCancellationCause()Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->getFinalRootCause(Lkotlinx/coroutines/JobSupport$Finishing;Ljava/util/List;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport;->getKey()Lkotlin/coroutines/CoroutineContext$Key;
HSPLkotlinx/coroutines/JobSupport;->getOnCancelComplete$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobSupport;->getOrPromoteCancellingList(Lkotlinx/coroutines/Incomplete;)Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobSupport;->getParentHandle$kotlinx_coroutines_core()Lkotlinx/coroutines/ChildHandle;
HSPLkotlinx/coroutines/JobSupport;->getState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->initParentJobInternal$kotlinx_coroutines_core(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobSupport;->invokeOnCompletion(Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/JobSupport;->invokeOnCompletion(ZZLkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/JobSupport;->isActive()Z
HSPLkotlinx/coroutines/JobSupport;->isCompleted()Z
HSPLkotlinx/coroutines/JobSupport;->isScopedCoroutine()Z
HSPLkotlinx/coroutines/JobSupport;->makeCancelling(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->makeCompletingOnce$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->makeNode(Lkotlin/jvm/functions/Function1;Z)Lkotlinx/coroutines/JobNode;
HSPLkotlinx/coroutines/JobSupport;->minusKey(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/JobSupport;->nextChild(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Lkotlinx/coroutines/ChildHandleNode;
HSPLkotlinx/coroutines/JobSupport;->notifyCancelling(Lkotlinx/coroutines/NodeList;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->notifyCompletion(Lkotlinx/coroutines/NodeList;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->onCancelling(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->onCompletionInternal(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->parentCancelled(Lkotlinx/coroutines/ParentJob;)V
HSPLkotlinx/coroutines/JobSupport;->promoteSingleToNodeList(Lkotlinx/coroutines/JobNode;)V
HSPLkotlinx/coroutines/JobSupport;->removeNode$kotlinx_coroutines_core(Lkotlinx/coroutines/JobNode;)V
HSPLkotlinx/coroutines/JobSupport;->setParentHandle$kotlinx_coroutines_core(Lkotlinx/coroutines/ChildHandle;)V
HSPLkotlinx/coroutines/JobSupport;->start()Z
HSPLkotlinx/coroutines/JobSupport;->startInternal(Ljava/lang/Object;)I
HSPLkotlinx/coroutines/JobSupport;->toCancellationException(Ljava/lang/Throwable;Ljava/lang/String;)Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->tryFinalizeSimpleState(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobSupport;->tryMakeCancelling(Lkotlinx/coroutines/Incomplete;Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->tryMakeCompleting(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->tryMakeCompletingSlowPath(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupportKt;->access$getCOMPLETING_ALREADY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getCOMPLETING_RETRY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getEMPTY_ACTIVE$p()Lkotlinx/coroutines/Empty;
HSPLkotlinx/coroutines/JobSupportKt;->access$getSEALED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getTOO_LATE_TO_CANCEL$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->boxIncomplete(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupportKt;->unboxState(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/MainCoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/NodeList;-><init>()V
HSPLkotlinx/coroutines/NodeList;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/NodeList;->isActive()Z
HSPLkotlinx/coroutines/NonDisposableHandle;-><init>()V
HSPLkotlinx/coroutines/NonDisposableHandle;->dispose()V
HSPLkotlinx/coroutines/RemoveOnCancel;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/StandaloneCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Z)V
HSPLkotlinx/coroutines/ThreadLocalEventLoop;-><init>()V
HSPLkotlinx/coroutines/ThreadLocalEventLoop;->getEventLoop$kotlinx_coroutines_core()Lkotlinx/coroutines/EventLoop;
HSPLkotlinx/coroutines/ThreadLocalEventLoop;->setEventLoop$kotlinx_coroutines_core(Lkotlinx/coroutines/EventLoop;)V
HSPLkotlinx/coroutines/TimeSourceKt;->getTimeSource()Lkotlinx/coroutines/TimeSource;
HSPLkotlinx/coroutines/Unconfined;-><init>()V
HSPLkotlinx/coroutines/UndispatchedCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/UndispatchedMarker;-><init>()V
HSPLkotlinx/coroutines/UndispatchedMarker;->fold(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/UndispatchedMarker;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/UndispatchedMarker;->getKey()Lkotlin/coroutines/CoroutineContext$Key;
HSPLkotlinx/coroutines/YieldKt;->checkCompletion(Lkotlin/coroutines/CoroutineContext;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;Z)V
HSPLkotlinx/coroutines/android/HandlerContext;->getImmediate()Lkotlinx/coroutines/android/HandlerContext;
HSPLkotlinx/coroutines/android/HandlerContext;->getImmediate()Lkotlinx/coroutines/android/HandlerDispatcher;
HSPLkotlinx/coroutines/android/HandlerContext;->isDispatchNeeded(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/android/HandlerDispatcher;-><init>()V
HSPLkotlinx/coroutines/android/HandlerDispatcher;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/android/HandlerDispatcherKt;->asHandler(Landroid/os/Looper;Z)Landroid/os/Handler;
HSPLkotlinx/coroutines/android/HandlerDispatcherKt;->from(Landroid/os/Handler;Ljava/lang/String;)Lkotlinx/coroutines/android/HandlerDispatcher;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;-><init>(Lkotlinx/coroutines/channels/AbstractChannel;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNext(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNextResult(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNextSuspend(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->next()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->setResult(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;-><init>(Lkotlinx/coroutines/CancellableContinuation;I)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->completeResumeReceive(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->resumeValue(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->tryResumeReceive(Ljava/lang/Object;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;-><init>(Lkotlinx/coroutines/channels/AbstractChannel$Itr;Lkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->completeResumeReceive(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->resumeOnCancellationFun(Ljava/lang/Object;)Lkotlin/jvm/functions/Function1;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->tryResumeReceive(Ljava/lang/Object;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractChannel$RemoveReceiveOnCancel;-><init>(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$RemoveReceiveOnCancel;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/channels/AbstractChannel;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->access$enqueueReceive(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->access$removeReceiveOnCancel(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->enqueueReceive(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->enqueueReceiveInternal(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->iterator()Lkotlinx/coroutines/channels/ChannelIterator;
HSPLkotlinx/coroutines/channels/AbstractChannel;->onReceiveDequeued()V
HSPLkotlinx/coroutines/channels/AbstractChannel;->onReceiveEnqueued()V
HSPLkotlinx/coroutines/channels/AbstractChannel;->pollInternal()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->receive(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->receiveSuspend(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->removeReceiveOnCancel(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->takeFirstReceiveOrPeekClosed()Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->completeResumeSend()V
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->getPollResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->tryResumeSend(Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->getClosedForSend()Lkotlinx/coroutines/channels/Closed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->getQueue()Lkotlinx/coroutines/internal/LockFreeLinkedListHead;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->offer(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->sendBuffered(Ljava/lang/Object;)Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->takeFirstReceiveOrPeekClosed()Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->takeFirstSendOrPeekClosed()Lkotlinx/coroutines/channels/Send;
HSPLkotlinx/coroutines/channels/BufferOverflow;-><init>(Ljava/lang/String;I)V
HSPLkotlinx/coroutines/channels/ChannelKt;->Channel$default(ILkotlinx/coroutines/channels/BufferOverflow;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/channels/Channel;
HSPLkotlinx/coroutines/channels/ChannelKt;->Channel(ILkotlinx/coroutines/channels/BufferOverflow;Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/channels/Channel;
HSPLkotlinx/coroutines/channels/ConflatedChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/ConflatedChannel;->enqueueReceiveInternal(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->isBufferAlwaysEmpty()Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->isBufferEmpty()Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/ConflatedChannel;->pollInternal()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/ConflatedChannel;->updateValueLocked(Ljava/lang/Object;)Lkotlinx/coroutines/internal/UndeliveredElementException;
HSPLkotlinx/coroutines/channels/LinkedListChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/LinkedListChannel;->isBufferAlwaysEmpty()Z
HSPLkotlinx/coroutines/channels/LinkedListChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/Receive;-><init>()V
HSPLkotlinx/coroutines/channels/Receive;->getOfferResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/Receive;->getOfferResult()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/Receive;->resumeOnCancellationFun(Ljava/lang/Object;)Lkotlin/jvm/functions/Function1;
HSPLkotlinx/coroutines/channels/Send;-><init>()V
HSPLkotlinx/coroutines/flow/AbstractFlow;-><init>()V
HSPLkotlinx/coroutines/flow/FlowKt;->first(Lkotlinx/coroutines/flow/Flow;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/FlowKt;->flow(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt;->take(Lkotlinx/coroutines/flow/Flow;I)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__BuildersKt;->flow(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__LimitKt$take$$inlined$unsafeFlow$1;-><init>(Lkotlinx/coroutines/flow/Flow;I)V
HSPLkotlinx/coroutines/flow/FlowKt__LimitKt;->take(Lkotlinx/coroutines/flow/Flow;I)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2$1;-><init>(Lkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/internal/Ref$ObjectRef;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$3;-><init>(Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt;->first(Lkotlinx/coroutines/flow/Flow;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SafeFlow;-><init>(Lkotlin/jvm/functions/Function2;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl$collect$1;-><init>(Lkotlinx/coroutines/flow/SharedFlowImpl;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl$collect$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;-><init>(IILkotlinx/coroutines/channels/BufferOverflow;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->access$tryPeekLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;Lkotlinx/coroutines/flow/SharedFlowSlot;)J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->awaitValue(Lkotlinx/coroutines/flow/SharedFlowSlot;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->cleanupTailLocked()V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->collect(Lkotlinx/coroutines/flow/FlowCollector;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlot()Lkotlinx/coroutines/flow/SharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/SharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->enqueueLocked(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->findSlotsToResumeLocked([Lkotlin/coroutines/Continuation;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getBufferEndIndex()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getHead()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getPeekedValueLockedAt(J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getQueueEndIndex()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getReplaySize()I
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getTotalSize()I
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->growBuffer([Ljava/lang/Object;II)[Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmit(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmitLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmitNoCollectorsLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryPeekLocked(Lkotlinx/coroutines/flow/SharedFlowSlot;)J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryTakeValue(Lkotlinx/coroutines/flow/SharedFlowSlot;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateBufferLocked(JJJJ)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateCollectorIndexLocked$kotlinx_coroutines_core(J)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateNewCollectorIndexLocked$kotlinx_coroutines_core()J
HSPLkotlinx/coroutines/flow/SharedFlowKt;->MutableSharedFlow$default(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/MutableSharedFlow;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->MutableSharedFlow(IILkotlinx/coroutines/channels/BufferOverflow;)Lkotlinx/coroutines/flow/MutableSharedFlow;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->access$getBufferAt([Ljava/lang/Object;J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->access$setBufferAt([Ljava/lang/Object;JLjava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowKt;->getBufferAt([Ljava/lang/Object;J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->setBufferAt([Ljava/lang/Object;JLjava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowSlot;-><init>()V
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->allocateLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->allocateLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;)Z
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->freeLocked(Ljava/lang/Object;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->freeLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/StateFlowImpl$collect$1;-><init>(Lkotlinx/coroutines/flow/StateFlowImpl;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl$collect$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl;->collect(Lkotlinx/coroutines/flow/FlowCollector;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlot()Lkotlinx/coroutines/flow/StateFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/StateFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->getValue()Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->setValue(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl;->updateState(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowKt;->MutableStateFlow(Ljava/lang/Object;)Lkotlinx/coroutines/flow/MutableStateFlow;
HSPLkotlinx/coroutines/flow/StateFlowKt;->access$getNONE$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/flow/StateFlowKt;->access$getPENDING$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/flow/StateFlowSlot;-><init>()V
HSPLkotlinx/coroutines/flow/StateFlowSlot;->allocateLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowSlot;->allocateLocked(Lkotlinx/coroutines/flow/StateFlowImpl;)Z
HSPLkotlinx/coroutines/flow/StateFlowSlot;->awaitPending(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowSlot;->makePending()V
HSPLkotlinx/coroutines/flow/StateFlowSlot;->takePending()Z
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;-><init>()V
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->access$getNCollectors$p(Lkotlinx/coroutines/flow/internal/AbstractSharedFlow;)I
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->access$getSlots$p(Lkotlinx/coroutines/flow/internal/AbstractSharedFlow;)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->allocateSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->freeSlot(Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;)V
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->getNCollectors()I
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->getSlots()[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;-><init>()V
HSPLkotlinx/coroutines/internal/AtomicOp;-><init>()V
HSPLkotlinx/coroutines/internal/AtomicOp;->decide(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/AtomicOp;->perform(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ContextScope;-><init>(Lkotlin/coroutines/CoroutineContext;)V
HSPLkotlinx/coroutines/internal/ContextScope;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;-><init>(Lkotlinx/coroutines/CoroutineDispatcher;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->checkPostponedCancellation(Lkotlinx/coroutines/CancellableContinuation;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->claimReusableCancellableContinuation()Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getDelegate$kotlinx_coroutines_core()Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getReusableCancellableContinuation()Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->isReusable(Lkotlinx/coroutines/CancellableContinuationImpl;)Z
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->postponeCancellation(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->takeState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/DispatchedContinuationKt;->access$getUNDEFINED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/internal/DispatchedContinuationKt;->resumeCancellableWith(Lkotlin/coroutines/Continuation;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListHead;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListHead;->isRemoved()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListKt;->unwrap(Ljava/lang/Object;)Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;->complete(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;->complete(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->access$finishAdd(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->addNext(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->addOneIfEmpty(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->correctPrev(Lkotlinx/coroutines/internal/OpDescriptor;)Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->finishAdd(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getNext()Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getNextNode()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getPrevNode()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->isRemoved()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->remove()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removeFirstOrNull()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removeOrNext()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removed()Lkotlinx/coroutines/internal/Removed;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->tryCondAddNext(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;)I
HSPLkotlinx/coroutines/internal/LockFreeTaskQueue;-><init>(Z)V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore$Companion;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore;-><init>(IZ)V
HSPLkotlinx/coroutines/internal/OpDescriptor;-><init>()V
HSPLkotlinx/coroutines/internal/Removed;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;->afterResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;->isScopedCoroutine()Z
HSPLkotlinx/coroutines/internal/Symbol;-><init>(Ljava/lang/String;)V
HSPLkotlinx/coroutines/internal/SystemPropsKt;->getAVAILABLE_PROCESSORS()I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp$default(Ljava/lang/String;IIIILjava/lang/Object;)I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp$default(Ljava/lang/String;JJJILjava/lang/Object;)J
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;)Ljava/lang/String;
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;III)I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;JJJ)J
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemPropsKt;->getAVAILABLE_PROCESSORS()I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemPropsKt;->systemProp(Ljava/lang/String;)Ljava/lang/String;
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp$default(Ljava/lang/String;IIIILjava/lang/Object;)I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp$default(Ljava/lang/String;JJJILjava/lang/Object;)J
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp(Ljava/lang/String;III)I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp(Ljava/lang/String;JJJ)J
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;->invoke(Ljava/lang/Object;Lkotlin/coroutines/CoroutineContext$Element;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt$findOne$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt$updateState$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt;->restoreThreadContext(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/ThreadContextKt;->threadContextElements(Lkotlin/coroutines/CoroutineContext;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt;->updateThreadContext(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->addImpl(Lkotlinx/coroutines/internal/ThreadSafeHeapNode;)V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->firstImpl()Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->getSize()I
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->isEmpty()Z
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->peek()Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->realloc()[Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->removeAtImpl(I)Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->setSize(I)V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->siftUpFrom(I)V
HSPLkotlinx/coroutines/intrinsics/CancellableKt;->startCoroutineCancellable$default(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/intrinsics/CancellableKt;->startCoroutineCancellable(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/intrinsics/UndispatchedKt;->startCoroutineUndispatched(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/intrinsics/UndispatchedKt;->startUndispatchedOrReturn(Lkotlinx/coroutines/internal/ScopeCoroutine;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler$Companion;-><init>()V
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler;-><init>(IIJLjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/DefaultScheduler;-><init>()V
HSPLkotlinx/coroutines/scheduling/DefaultScheduler;->getIO()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IIJLjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IILjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IILjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;->createScheduler()Lkotlinx/coroutines/scheduling/CoroutineScheduler;
HSPLkotlinx/coroutines/scheduling/GlobalQueue;-><init>()V
HSPLkotlinx/coroutines/scheduling/LimitingDispatcher;-><init>(Lkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;ILjava/lang/String;I)V
HSPLkotlinx/coroutines/scheduling/NanoTimeSource;-><init>()V
HSPLkotlinx/coroutines/scheduling/NonBlockingContext;-><init>()V
HSPLkotlinx/coroutines/scheduling/NonBlockingContext;->afterTask()V
HSPLkotlinx/coroutines/scheduling/SchedulerTimeSource;-><init>()V
HSPLkotlinx/coroutines/scheduling/Task;-><init>()V
HSPLkotlinx/coroutines/scheduling/Task;-><init>(JLkotlinx/coroutines/scheduling/TaskContext;)V
HSPLkotlinx/coroutines/sync/Empty;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->lock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;Lkotlin/coroutines/Continuation;ILjava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->tryLock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;ILjava/lang/Object;)Z
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->unlock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont$tryResumeLockWaiter$1;-><init>(Lkotlinx/coroutines/sync/MutexImpl$LockCont;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;-><init>(Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;->completeResumeLockWaiter(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;->tryResumeLockWaiter()Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl$LockWaiter;-><init>(Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockedQueue;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/sync/MutexImpl$LockCont;Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;-><init>(Z)V
HSPLkotlinx/coroutines/sync/MutexImpl;->lock(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;->lockSuspend(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;->tryLock(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/sync/MutexImpl;->unlock(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexKt;->Mutex$default(ZILjava/lang/Object;)Lkotlinx/coroutines/sync/Mutex;
HSPLkotlinx/coroutines/sync/MutexKt;->Mutex(Z)Lkotlinx/coroutines/sync/Mutex;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getEMPTY_LOCKED$p()Lkotlinx/coroutines/sync/Empty;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getEMPTY_UNLOCKED$p()Lkotlinx/coroutines/sync/Empty;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getLOCKED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getUNLOCKED$p()Lkotlinx/coroutines/internal/Symbol;

# Baseline Profile Rules for androidx.startup

Landroidx/startup/AppInitializer;
HSPLandroidx/startup/AppInitializer;->**(**)**
