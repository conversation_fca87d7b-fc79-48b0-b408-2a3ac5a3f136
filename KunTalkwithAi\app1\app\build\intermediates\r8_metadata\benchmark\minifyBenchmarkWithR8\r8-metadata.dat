{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": false, "isRuntimeInvisibleParameterAnnotationsKept": false, "isRuntimeInvisibleTypeAnnotationsKept": false, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "27", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 68215134900, "numberOfThreads": 18}, "dexFiles": [{"checksum": "0f4cfead512742efdf196f7f2b647a1f85d982b854a36f4a337ea105006d982b", "startup": false}], "stats": {"noObfuscationPercentage": 4.23, "noOptimizationPercentage": 4.72, "noShrinkingPercentage": 4.15}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.11.18"}