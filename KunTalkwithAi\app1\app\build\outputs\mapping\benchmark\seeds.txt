com.example.everytalk.data.DataClass.Part$Text$Companion
androidx.compose.foundation.MagnifierElement
com.example.everytalk.data.DataClass.GithubRelease$Companion
com.example.everytalk.data.DataClass.ModalityType
com.example.everytalk.data.DataClass.ThinkingConfig
com.example.everytalk.data.DataClass.GeminiApiRequest
androidx.compose.foundation.layout.BoxChildDataElement
com.example.everytalk.data.DataClass.GithubRelease$$serializer
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer
androidx.compose.foundation.text.input.internal.LegacyAdaptingPlatformTextInputModifier
com.example.everytalk.data.DataClass.GenerationConfig$$serializer
com.example.everytalk.data.DataClass.ChatRequest
com.example.everytalk.data.DataClass.SafetyRating
com.example.everytalk.data.DataClass.Message$$serializer
androidx.compose.ui.draw.DrawWithContentElement
androidx.compose.ui.layout.LayoutElement
androidx.compose.animation.SizeAnimationModifierElement
com.example.everytalk.data.DataClass.Candidate$Companion
com.example.everytalk.data.DataClass.PromptFeedback$Companion
androidx.navigation.compose.BackStackEntryIdViewModel
androidx.core.content.FileProvider
com.example.everytalk.data.DataClass.IMessage
coil3.compose.internal.ContentPainterElement
com.example.everytalk.data.DataClass.PartsApiMessage$Companion
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.compose.foundation.text.modifiers.TextStringSimpleElement
androidx.compose.foundation.lazy.layout.LazyLayoutBeyondBoundsModifierElement
androidx.compose.foundation.layout.IntrinsicWidthElement
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.compose.foundation.layout.UnspecifiedConstraintsElement
kotlin.coroutines.jvm.internal.BaseContinuationImpl
com.example.everytalk.data.DataClass.Part$InlineData
androidx.compose.foundation.layout.HorizontalAlignElement
androidx.lifecycle.ReportFragment$LifecycleCallbacks
com.example.everytalk.data.DataClass.Part$FileUri
com.example.everytalk.data.DataClass.ContentPart$Audio
androidx.compose.foundation.HoverableElement
com.example.everytalk.data.DataClass.SafetySetting$$serializer
com.example.everytalk.data.DataClass.Candidate$$serializer
androidx.compose.ui.input.pointer.PointerInputEventHandler
androidx.core.app.RemoteActionCompatParcelizer
androidx.compose.ui.draganddrop.AndroidDragAndDropManager$modifier$1
androidx.compose.foundation.IndicationModifierElement
com.example.everytalk.data.DataClass.ContentPart$Code
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt
com.example.everytalk.data.DataClass.Sender$Companion
androidx.lifecycle.ProcessLifecycleOwner$attach$1
com.example.everytalk.data.DataClass.Content$Companion
androidx.compose.foundation.ClickableElement
androidx.compose.ui.input.nestedscroll.NestedScrollElement
com.example.everytalk.data.DataClass.WebSearchResult$Companion
androidx.startup.InitializationProvider
androidx.compose.foundation.lazy.layout.TraversablePrefetchStateModifierElement
androidx.profileinstaller.ProfileInstallReceiver
androidx.compose.foundation.relocation.BringIntoViewRequesterElement
androidx.compose.ui.semantics.AppendedSemanticsElement
androidx.compose.foundation.gestures.ScrollableElement
com.example.everytalk.data.DataClass.ContentPart$Html
androidx.compose.ui.layout.LayoutIdElement
com.google.gson.reflect.TypeToken
androidx.compose.foundation.text.modifiers.TextAnnotatedStringElement
com.example.everytalk.data.DataClass.Part$Companion
com.example.everytalk.data.DataClass.Part$FileUri$Companion
androidx.compose.material3.internal.DraggableAnchorsElement
androidx.compose.foundation.lazy.layout.LazyLayoutItemAnimator$DisplayingDisappearingItemsElement
com.example.everytalk.data.DataClass.ApiContentPart$Text
com.example.everytalk.data.DataClass.Message$Companion
androidx.profileinstaller.ProfileInstallerInitializer
com.example.everytalk.data.DataClass.Message
com.example.everytalk.data.DataClass.Part$FileUri$$serializer
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion
androidx.compose.ui.focus.FocusOwnerImpl$modifier$1
com.example.everytalk.data.DataClass.SimpleTextApiMessage
com.example.everytalk.data.DataClass.PromptFeedback$$serializer
androidx.navigation.NavGraphNavigator
androidx.compose.foundation.ScrollingLayoutElement
androidx.navigation.compose.DialogNavigator
androidx.compose.animation.EnterExitTransitionElement
com.example.everytalk.data.DataClass.Candidate
androidx.compose.foundation.BackgroundElement
androidx.emoji2.text.EmojiCompatInitializer
androidx.navigation.compose.ComposeNavigator
com.example.everytalk.data.DataClass.Sender
androidx.compose.foundation.layout.AlignmentLineOffsetDpElement
androidx.compose.ui.graphics.GraphicsLayerElement
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings
androidx.compose.foundation.layout.PaddingElement
com.example.everytalk.data.DataClass.SafetySetting$Companion
com.example.everytalk.data.DataClass.PromptFeedback
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion
androidx.compose.foundation.layout.VerticalAlignElement
androidx.lifecycle.SavedStateHandlesVM
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
androidx.compose.ui.input.pointer.StylusHoverIconModifierElement
androidx.compose.foundation.lazy.layout.LazyLayoutSemanticsModifier
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion
androidx.lifecycle.ReportFragment
com.example.everytalk.data.DataClass.AbstractApiMessage
androidx.lifecycle.ProcessLifecycleInitializer
androidx.compose.ui.semantics.EmptySemanticsElement
androidx.navigation.NavControllerViewModel
com.example.everytalk.data.DataClass.ApiConfig$Companion
androidx.compose.ui.input.pointer.SuspendPointerInputElement
com.example.everytalk.data.DataClass.Part$Text
androidx.compose.foundation.layout.SizeElement
com.example.everytalk.data.DataClass.ApiContentPart$Companion
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion
androidx.compose.ui.input.key.KeyInputElement
androidx.compose.foundation.text.input.internal.CoreTextFieldSemanticsModifier
androidx.compose.ui.layout.OnGloballyPositionedElement
androidx.compose.material3.MinimumInteractiveModifier
androidx.graphics.path.ConicConverter
com.example.everytalk.data.DataClass.Content
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion
okhttp3.internal.publicsuffix.PublicSuffixDatabase
android.support.v4.graphics.drawable.IconCompatParcelizer
com.example.everytalk.data.DataClass.GenerationConfig
androidx.navigation.compose.ComposeNavGraphNavigator
com.example.everytalk.data.DataClass.MessageKt$WhenMappings
androidx.core.app.CoreComponentFactory
androidx.compose.ui.draw.PainterElement
androidx.navigation.NavBackStackEntry$SavedStateViewModel
androidx.compose.foundation.layout.PaddingValuesElement
com.example.everytalk.data.DataClass.ApiConfig
androidx.compose.foundation.BorderModifierNodeElement
androidx.compose.foundation.text.handwriting.StylusHandwritingElement
androidx.versionedparcelable.ParcelImpl
androidx.graphics.path.PathIteratorPreApi34Impl
androidx.compose.foundation.layout.FillElement
android.support.v4.app.RemoteActionCompatParcelizer
androidx.versionedparcelable.CustomVersionedParcelable
androidx.core.graphics.drawable.IconCompat
androidx.compose.foundation.FocusableElement
com.example.everytalk.data.DataClass.WebSearchResult
androidx.compose.ui.graphics.BlockGraphicsLayerElement
com.example.everytalk.data.DataClass.ThinkingConfig$Companion
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.compose.ui.focus.FocusChangedElement
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
androidx.compose.foundation.CombinedClickableElement
com.example.everytalk.data.DataClass.ApiConfig$$serializer
androidx.core.app.RemoteActionCompat
androidx.compose.foundation.layout.LayoutWeightElement
androidx.compose.ui.semantics.ClearAndSetSemanticsElement
androidx.compose.ui.focus.FocusRequesterElement
com.example.everytalk.data.DataClass.ModalityType$Companion
com.example.everytalk.data.DataClass.SafetyRating$$serializer
androidx.compose.ui.input.pointer.PointerHoverIconModifierElement
androidx.annotation.Keep
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion
com.example.everytalk.statecontroller.MainActivity
com.example.everytalk.data.DataClass.ApiContentPart$FileUri
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer
com.example.everytalk.data.DataClass.Content$$serializer
androidx.compose.ui.platform.AndroidComposeView$bringIntoViewNode$1
com.example.everytalk.data.DataClass.Part$InlineData$Companion
androidx.compose.foundation.layout.WrapContentElement
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer
com.example.everytalk.data.DataClass.WebSearchResult$$serializer
io.ktor.client.engine.android.AndroidEngineContainer
com.example.everytalk.data.DataClass.Part$Text$$serializer
androidx.compose.foundation.gestures.DraggableElement
com.example.everytalk.data.DataClass.ChatRequest$Companion
androidx.compose.animation.AnimatedContentTransitionScopeImpl$SizeModifierElement
com.example.everytalk.data.DataClass.MessageKt
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer
androidx.navigation.ActivityNavigator
com.example.everytalk.data.DataClass.ApiContentPart$InlineData
com.example.everytalk.data.DataClass.PartsApiMessage
androidx.compose.ui.draw.DrawBehindElement
androidx.compose.ui.layout.OnSizeChangedModifier
androidx.compose.foundation.text.modifiers.SelectableTextAnnotatedStringElement
com.example.everytalk.data.DataClass.SafetyRating$Companion
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
com.example.everytalk.data.DataClass.ChatRequest$$serializer
androidx.compose.ui.draw.DrawWithCacheElement
com.example.everytalk.data.DataClass.SafetySetting
androidx.compose.ui.draw.ShadowGraphicsLayerElement
com.example.everytalk.data.DataClass.Part$InlineData$$serializer
com.example.everytalk.data.DataClass.ContentPart
com.example.everytalk.statecontroller.LRUCache
com.example.everytalk.data.DataClass.GithubRelease
androidx.compose.foundation.ScrollingContainerElement
androidx.compose.foundation.layout.OffsetElement
com.example.everytalk.data.DataClass.GenerationConfig$Companion
androidx.compose.ui.input.rotary.RotaryInputElement
com.example.everytalk.data.DataClass.ApiContentPart
com.example.everytalk.data.DataClass.Part
com.example.everytalk.data.DataClass.GeminiApiResponse
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String role
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForRead$internal
com.example.everytalk.data.DataClass.Message: long timestamp
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Text: com.example.everytalk.data.network.AppStreamEvent$Text$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.MessageKt$WhenMappings: int[] $EnumSwitchMapping$0
com.example.everytalk.data.DataClass.ContentPart$Audio: int $stable
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message$$serializer INSTANCE
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Message: java.lang.String reasoning
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String mimeType
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.example.everytalk.data.DataClass.Part$FileUri: int $stable
com.example.everytalk.data.DataClass.ChatRequest: int $stable
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: int $stable
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean includeThoughts
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating$$serializer INSTANCE
com.example.everytalk.data.network.AppStreamEvent$ToolCall$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus: com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion Companion
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated$volatile
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.WebSearchResult: int $stable
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender User
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment$volatile
kotlinx.coroutines.EventLoopImplBase: int _isCompleted$volatile
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType TEXT
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String id
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String contentId
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String title
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiContentPart: int $stable
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Error$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customExtraBody
com.example.everytalk.statecontroller.LRUCache: int $stable
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.Part: int $stable
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd$volatile
com.example.everytalk.data.DataClass.Candidate$$serializer: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$Text: int $stable
io.ktor.util.collections.CopyOnWriteHashMap: java.lang.Object current
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String text
com.example.everytalk.data.DataClass.GeminiApiResponse: int $stable
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer INSTANCE
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String threshold
com.example.everytalk.data.DataClass.Part$Text$$serializer: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List candidates
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag$volatile
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur$volatile
com.example.everytalk.data.DataClass.Part: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiAddress
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback$$serializer INSTANCE
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String provider
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String role
com.example.everytalk.data.DataClass.Message: java.lang.String currentWebSearchStage
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest$Companion Companion
com.example.everytalk.models.SelectedMediaItem$GenericFile: com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion Companion
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String contentId
com.example.everytalk.data.DataClass.ChatRequest$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetyRating$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String probability
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String content
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig: float temperature
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder$volatile
io.ktor.utils.io.pool.SingleInstancePool: int borrowed
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String uri
kotlinx.serialization.json.JsonObject: kotlinx.serialization.json.JsonObject$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: int $stable
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _closed
io.ktor.utils.io.ByteBufferChannel: int writeSuspensionSize
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _writeOp
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float topP
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $VALUES
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause$volatile
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate$Companion Companion
com.example.everytalk.data.network.AppStreamEvent: com.example.everytalk.data.network.AppStreamEvent$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int $stable
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer INSTANCE
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String name
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text$$serializer INSTANCE
com.example.everytalk.data.DataClass.WebSearchResult: int index
com.example.everytalk.data.DataClass.ContentPart: int $stable
io.ktor.utils.io.internal.RingBufferCapacity: int _availableForWrite$internal
com.example.everytalk.data.network.AppStreamEvent$StreamEnd: com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion Companion
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.Part$Text: int $stable
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus$volatile
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer thinkingBudget
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String data
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int $stable
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy $cachedSerializer$delegate
kotlinx.coroutines.CancelledContinuation: int _resumed$volatile
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri$Companion Companion
io.ktor.utils.io.core.internal.ChunkBuffer: java.lang.Object nextRef
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String category
com.example.everytalk.data.DataClass.GenerationConfig: int $stable
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String provider
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] $childSerializers
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: int _availablePermits$volatile
androidx.compose.foundation.lazy.layout.DefaultLazyKey: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content content
com.example.everytalk.data.network.AppStreamEvent$Content: com.example.everytalk.data.network.AppStreamEvent$Content$Companion Companion
kotlinx.serialization.json.JsonElement: kotlinx.serialization.json.JsonElement$Companion Companion
com.example.everytalk.data.DataClass.Part$Text: java.lang.String text
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map customModelParameters
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set customProviders
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _state
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer maxOutputTokens
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content$$serializer INSTANCE
androidx.compose.runtime.ParcelableSnapshotMutableState: android.os.Parcelable$Creator CREATOR
io.ktor.client.call.HttpClientCall: int received
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String imageSize
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack$volatile
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle$volatile
io.ktor.utils.io.core.internal.ChunkBuffer: int refCount
com.example.everytalk.data.network.AppStreamEvent$Reasoning: com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion Companion
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object jobCancellationHandler
kotlinx.coroutines.flow.ChannelAsFlow: int consumed$volatile
com.example.everytalk.data.DataClass.Message: java.lang.String name
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content$Companion Companion
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender sender
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage$$serializer INSTANCE
kotlinx.coroutines.internal.Segment: int cleanedAndPointers$volatile
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate$$serializer INSTANCE
kotlinx.coroutines.EventLoopImplBase$DelayedTask: java.lang.Object _heap
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: int $stable
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List parts
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion Companion
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
io.ktor.utils.io.ByteBufferChannel: kotlinx.coroutines.Job attachedJob
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion Companion
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer INSTANCE
kotlinx.coroutines.CompletedExceptionally: int _handled$volatile
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] $childSerializers
io.ktor.utils.io.jvm.javaio.BlockingAdapter: java.lang.Object state
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: int $stable
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: int $stable
com.example.everytalk.data.DataClass.GithubRelease: int $stable
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String body
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String name
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.serialization.json.JsonArray: kotlinx.serialization.json.JsonArray$Companion Companion
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: int $stable
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult$Companion Companion
com.example.everytalk.data.DataClass.Message: java.util.List imageUrls
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String data
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex$volatile
com.example.everytalk.data.DataClass.ApiConfig$$serializer: int $stable
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap: com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List safetySettings
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean defaultUseWebSearch
com.example.everytalk.data.DataClass.Candidate: java.util.List safetyRatings
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.example.everytalk.data.DataClass.Candidate: java.lang.String finishReason
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int $stable
com.example.everytalk.data.DataClass.Message: java.util.List webSearchResults
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender$Companion Companion
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String id
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment$volatile
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef$volatile
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev$volatile
com.example.everytalk.models.SelectedMediaItem$Audio: com.example.everytalk.models.SelectedMediaItem$Audio$Companion Companion
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig thinkingConfig
androidx.lifecycle.Lifecycle$Event: kotlin.enums.EnumEntries $ENTRIES
io.ktor.utils.io.internal.CancellableReusableContinuation: java.lang.Object state
kotlinx.serialization.json.JsonPrimitive: kotlinx.serialization.json.JsonPrimitive$Companion Companion
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String mimeType
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult$$serializer INSTANCE
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType MULTIMODAL
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next$volatile
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation$volatile
kotlinx.serialization.json.JsonNull: kotlinx.serialization.json.JsonNull INSTANCE
com.example.everytalk.data.DataClass.ContentPart$Code: int $stable
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String key
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String name
io.ktor.utils.io.pool.DefaultPool: long top
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String mimeType
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List contents
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler$volatile
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback$Companion Companion
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType$Companion Companion
com.example.everytalk.data.DataClass.Content: int $stable
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state$volatile
com.example.everytalk.data.DataClass.ApiContentPart: com.example.everytalk.data.DataClass.ApiContentPart$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType modalityType
androidx.compose.runtime.ParcelableSnapshotMutableFloatState: android.os.Parcelable$Creator CREATOR
androidx.compose.runtime.ParcelableSnapshotMutableIntState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String mimeType
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.models.SelectedMediaItem$GenericFile$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SafetySetting: int $stable
com.example.everytalk.data.DataClass.ModalityType: java.lang.String displayName
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle$volatile
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask$volatile
com.example.everytalk.models.SelectedMediaItem: com.example.everytalk.models.SelectedMediaItem$Companion Companion
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer INSTANCE
com.example.everytalk.data.network.ApiClient$FileMetadata: com.example.everytalk.data.network.ApiClient$FileMetadata$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse$Companion Companion
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String href
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List safetyRatings
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.example.everytalk.data.DataClass.ContentPart: java.lang.String contentId
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl$volatile
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String markdownWithKatex
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData$Companion Companion
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender Tool
io.ktor.utils.io.internal.RingBufferCapacity: int _pendingToFlush
androidx.compose.runtime.ParcelableSnapshotMutableLongState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.ThreadState: int _state$volatile
kotlinx.coroutines.JobSupport: java.lang.Object _state$volatile
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: int $stable
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig$Companion Companion
com.example.everytalk.models.SelectedMediaItem$ImageFromUri: com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Reasoning$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting$$serializer INSTANCE
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List apiConfigs
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed$volatile
io.ktor.utils.io.jvm.javaio.BlockingAdapter: int result
io.ktor.utils.io.ByteBufferChannel: long totalBytesWritten
com.example.everytalk.data.network.AppStreamEvent$ToolCall: com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion Companion
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig generationConfig
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: int $stable
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage$Companion Companion
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
io.ktor.utils.io.ByteBufferChannel: java.lang.Object _readOp
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String id
com.example.everytalk.data.DataClass.ThinkingConfig: int $stable
com.example.everytalk.data.DataClass.Content: java.util.List parts
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object toolChoice
com.example.everytalk.data.DataClass.Content: java.lang.String role
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long deqIdx$volatile
com.example.everytalk.data.DataClass.SafetyRating: int $stable
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer maxTokens
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float guidanceScale
com.example.everytalk.data.DataClass.Content$$serializer: int $stable
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] $childSerializers
io.ktor.utils.io.ByteBufferChannel: io.ktor.utils.io.internal.JoiningState joining
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType VIDEO
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message: boolean isError
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.example.everytalk.data.DataClass.Message: java.util.List attachments
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String code
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.example.everytalk.data.DataClass.Part: com.example.everytalk.data.DataClass.Part$Companion Companion
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer$volatile
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer numInferenceSteps
com.example.everytalk.data.DataClass.Candidate: int index
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.ChatRequest: java.util.List messages
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex$volatile
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: long enqIdx$volatile
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.example.everytalk.data.network.AppStreamEvent$Finish: com.example.everytalk.data.network.AppStreamEvent$Finish$Companion Companion
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String fileUri
androidx.navigation.NavBackStackEntryState: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.Candidate: int $stable
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] $childSerializers
io.ktor.utils.io.pool.SingleInstancePool: int disposed
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers$volatile
com.example.everytalk.data.DataClass.ContentPart$Html: int $stable
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String htmlUrl
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object tail$volatile
com.example.everytalk.statecontroller.LRUCache: int maxSize
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest$Companion Companion
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String tagName
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse: com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion Companion
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String contentId
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String base64Data
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex$volatile
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean useWebSearch
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri$$serializer INSTANCE
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.GeminiApiRequest: int $stable
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting$volatile
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $VALUES
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender AI
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.example.everytalk.data.DataClass.AbstractApiMessage: int $stable
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] $childSerializers
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev$volatile
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String category
com.example.everytalk.data.DataClass.ApiConfig: int $stable
coil3.compose.internal.DeferredDispatchCoroutineDispatcher: int _unconfined$volatile
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next$volatile
io.ktor.client.engine.HttpClientEngineBase: int closed
io.ktor.utils.io.ByteBufferChannel: long totalBytesRead
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.sync.SemaphoreAndMutexImpl: java.lang.Object head$volatile
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner$volatile
com.example.everytalk.data.network.ApiClient$FileMetadata$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState$volatile
io.ktor.utils.io.pool.SingleInstancePool: java.lang.Object instance
com.example.everytalk.data.DataClass.Message: int $stable
kotlinx.coroutines.internal.ThreadSafeHeap: int _size$volatile
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String model
kotlin.coroutines.SafeContinuation: java.lang.Object result
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next$volatile
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String snippet
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback promptFeedback
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause$volatile
com.example.everytalk.data.network.AppStreamEvent$Finish$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType AUDIO
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment$volatile
kotlin.SafePublicationLazyImpl: java.lang.Object _value
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries $ENTRIES
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
kotlinx.coroutines.InvokeOnCancelling: int _invoked$volatile
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: int $stable
com.example.everytalk.data.DataClass.PromptFeedback: int $stable
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender System
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: int $stable
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String language
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] $childSerializers
com.example.everytalk.data.DataClass.PartsApiMessage: int $stable
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest$$serializer INSTANCE
com.example.everytalk.data.DataClass.Message: java.lang.String id
com.example.everytalk.data.DataClass.ChatRequest: java.util.List tools
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String address
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.AbstractApiMessage: com.example.everytalk.data.DataClass.AbstractApiMessage$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.models.SelectedMediaItem$Audio$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy $cachedSerializer$delegate
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: int $stable
com.example.everytalk.data.DataClass.Part$InlineData: int $stable
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue$volatile
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.example.everytalk.data.network.AppStreamEvent$Error: com.example.everytalk.data.network.AppStreamEvent$Error$Companion Companion
com.example.everytalk.data.DataClass.SafetySetting$$serializer: int $stable
io.ktor.util.pipeline.Pipeline: java.lang.Object _interceptors
com.example.everytalk.data.DataClass.Message: java.lang.String text
com.example.everytalk.data.DataClass.Message: boolean contentStarted
kotlinx.coroutines.channels.BufferedChannel: long receivers$volatile
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean forceGoogleReasoningPrompt
com.example.everytalk.data.DataClass.Message$$serializer: int $stable
kotlinx.coroutines.DispatchedCoroutine: int _decision$volatile
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig$Companion Companion
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults: com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion Companion
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String apiKey
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor descriptor
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message$Companion Companion
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float topP
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean qwenEnableSearch
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType IMAGE
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
io.ktor.client.HttpClient: int closed
coil3.RealImageLoader: int shutdown$volatile
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion Companion
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float temperature
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String model
com.example.everytalk.data.DataClass.GithubRelease$$serializer: int $stable
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.DrawChildContainer getContainer()
androidx.compose.ui.platform.AndroidComposeView: boolean getShowLayoutBounds()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult[] values()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntRect getVisibleDisplayBounds()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ClipboardManager getClipboardManager()
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: boolean equals(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer getMaxOutputTokens()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.compose.ui.platform.AndroidComposeViewAssistHelperMethodsO: void setClassName(android.view.ViewStructure,android.view.View)
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Part$InlineData: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.statecontroller.LRUCache: LRUCache(int)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: int hashCode()
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption[] values()
com.example.everytalk.data.DataClass.Candidate: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component14()
com.example.everytalk.data.DataClass.Part: Part()
com.example.everytalk.data.network.AppStreamEvent$Error$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Message: java.lang.String component7()
androidx.compose.ui.platform.AbstractComposeView: void setTransitionGroup(boolean)
com.example.everytalk.data.network.AppStreamEvent$WebSearchStatus$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidViewsHandler getAndroidViewsHandler$ui_release()
com.example.everytalk.data.DataClass.Sender: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy(int,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion()
androidx.compose.ui.platform.AbstractComposeView: void getShowLayoutBounds$annotations()
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.compose.runtime.collection.MutableVectorKt: void throwNegativeIndexException(int)
com.example.everytalk.data.DataClass.Message: java.lang.String component1()
androidx.compose.ui.window.PopupLayout: void setPopupContentSize-fhxjrPA(androidx.compose.ui.unit.IntSize)
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState[] values()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component2()
androidx.compose.runtime.ComposerKt: java.lang.Void composeRuntimeError(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight[] values()
com.example.everytalk.data.DataClass.GithubRelease: int hashCode()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer getThinkingBudget()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.ViewLayer: long getLayerId()
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.ChatRequest: void getProvider$annotations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
com.example.everytalk.data.DataClass.Message$$serializer: Message$$serializer()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void setShowLayoutBounds(boolean)
androidx.compose.ui.platform.AndroidComposeView: void setOnViewTreeOwnersAvailable(kotlin.jvm.functions.Function1)
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getUseWebSearch()
com.example.everytalk.data.DataClass.GithubRelease: void getBody$annotations()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34()
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomExtraBody()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getQwenEnableSearch()
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.RootForTest getRootForTest()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component2()
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy(java.util.List)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ChatRequest)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer serializer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
com.example.everytalk.data.DataClass.ApiConfig: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(java.util.List)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: com.example.everytalk.data.DataClass.GithubRelease deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult valueOf(java.lang.String)
androidx.compose.ui.graphics.layer.ViewLayer: androidx.compose.ui.graphics.CanvasHolder getCanvasHolder()
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getRole()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.compose.ui.window.PopupLayout: boolean getCanCalculatePosition()
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType[] values()
androidx.compose.ui.graphics.layer.ViewLayer: void setCanUseCompositingLayer$ui_graphics_release(boolean)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue[] values()
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
coil3.util.Logger$Level: coil3.util.Logger$Level[] values()
androidx.compose.ui.node.LayoutNode$LayoutState: androidx.compose.ui.node.LayoutNode$LayoutState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: ApiContentPart$FileUri$$serializer()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component2()
com.example.everytalk.models.ImageSourceOption: com.example.everytalk.models.ImageSourceOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getId()
androidx.core.view.WindowInsetsCompat$Impl34: boolean isVisible(int)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getCategory()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: GeminiApiResponse$$serializer()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent valueOf(java.lang.String)
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority[] values()
androidx.compose.ui.platform.ViewLayer: void setCameraDistancePx(float)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState valueOf(java.lang.String)
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: GenerationConfig$$serializer()
com.example.everytalk.models.SelectedMediaItem$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion()
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
com.example.everytalk.data.DataClass.Part$Text: com.example.everytalk.data.DataClass.Part$Text copy$default(com.example.everytalk.data.DataClass.Part$Text,java.lang.String,int,java.lang.Object)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(java.lang.String,java.lang.String)
androidx.graphics.path.PathIteratorPreApi34Impl: boolean internalPathIteratorHasNext(long)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy$default(com.example.everytalk.data.DataClass.ThinkingConfig,java.lang.Boolean,java.lang.Integer,int,java.lang.Object)
kotlinx.serialization.json.JsonElement$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.material3.DrawerValue: androidx.compose.material3.DrawerValue valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiResponse)
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy$default(com.example.everytalk.data.DataClass.Part$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage: void getId$annotations()
androidx.compose.material.ripple.RippleHostView: void setRippleState$lambda$2(androidx.compose.material.ripple.RippleHostView)
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.ui.platform.AndroidComposeView: void setLastMatrixRecalculationAnimationTime$ui_release(long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.graphics.Path getManualClipPath()
com.example.everytalk.data.DataClass.GithubRelease$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType getModalityType()
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: GeminiApiRequest$$serializer()
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String toString()
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.semantics.SemanticsOwner getSemanticsOwner()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTopP()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean getForceGoogleReasoningPrompt()
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy[] values()
com.example.everytalk.data.DataClass.ContentPart$Audio: ContentPart$Audio(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState[] values()
com.example.everytalk.data.DataClass.ModalityType: java.lang.String getDisplayName()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: com.example.everytalk.data.DataClass.GenerationConfig deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.WebSearchResult: int getIndex()
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion()
com.example.everytalk.data.DataClass.Content: int hashCode()
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] $values()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy$default(com.example.everytalk.data.DataClass.ApiConfig,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,java.lang.Object)
androidx.compose.foundation.MutatePriority: androidx.compose.foundation.MutatePriority valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
androidx.compose.ui.platform.AbstractComposeView: boolean getShowLayoutBounds()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component1()
com.example.everytalk.data.DataClass.ApiConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiAddress()
com.example.everytalk.data.DataClass.Message$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Message: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.platform.AndroidComposeView: void setLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.window.PopupPositionProvider getPositionProvider()
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.platform.ViewLayer: float getCameraDistancePx()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Message: java.util.List getWebSearchResults()
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight[] values()
com.example.everytalk.data.DataClass.PartsApiMessage: void getRole$annotations()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content component1()
com.example.everytalk.data.DataClass.PromptFeedback: int hashCode()
androidx.compose.ui.text.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.layout.Placeable$PlacementScope getPlacementScope()
com.example.everytalk.data.DataClass.Part$FileUri$Companion: Part$FileUri$Companion()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
com.example.everytalk.data.DataClass.GithubRelease: boolean equals(java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.Clipboard getClipboard()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy$default(com.example.everytalk.data.DataClass.Candidate,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component4()
com.example.everytalk.data.network.ApiClient$FileUploadInitialResponse$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.MotionEventVerifierApi29: boolean isValidMotionEvent(android.view.MotionEvent,int)
androidx.compose.animation.core.PreconditionsKt: void throwIllegalStateException(java.lang.String)
androidx.core.view.WindowInsetsCompat$TypeImpl34: int toPlatformType(int)
androidx.startup.InitializationProvider: InitializationProvider()
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(int,java.lang.Boolean,java.lang.Integer,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Message: java.util.List component12()
com.example.everytalk.data.DataClass.Part$InlineData: int hashCode()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component1()
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease$Companion: GithubRelease$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode[] values()
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidAccessibilityManager getAccessibilityManager()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.contentcapture.AndroidContentCaptureManager getContentCaptureManager$ui_release()
com.example.everytalk.data.DataClass.GenerationConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GenerationConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component6()
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy$default(com.example.everytalk.data.DataClass.ContentPart$Html,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getNumInferenceSteps()
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: com.example.everytalk.data.DataClass.SimpleTextApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
com.google.gson.reflect.TypeToken: TypeToken()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.ui.window.PopupLayout: kotlin.jvm.functions.Function2 getContent()
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy$default(com.example.everytalk.data.DataClass.ContentPart$Audio,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Candidate: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.IMessage: java.lang.String getName()
androidx.compose.ui.graphics.layer.ViewLayer: boolean getCanUseCompositingLayer$ui_graphics_release()
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeRegisterBackCallback(android.view.View,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getContent()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: void getLastMatrixRecalculationAnimationTime$ui_release$annotations()
androidx.activity.EdgeToEdgeApi28: void adjustLayoutInDisplayCutoutMode(android.view.Window)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.Part: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.network.ApiClient$FileMetadata$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.Content: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Message: java.lang.String getText()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(int,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component5()
com.example.everytalk.statecontroller.LRUCache: java.util.Collection values()
com.example.everytalk.data.DataClass.IMessage: java.lang.String getRole()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsO: void focusable(android.view.View,int,boolean)
androidx.navigation.compose.BackStackEntryIdViewModel: BackStackEntryIdViewModel(androidx.lifecycle.SavedStateHandle)
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void setViewTranslationCallback(android.view.View)
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component3()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getMarkdownWithKatex()
androidx.core.view.WindowInsetsCompat$Impl20: void setSystemUiVisibility(int)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object component10()
coil3.util.Logger$Level: coil3.util.Logger$Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Code: int hashCode()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.WebSearchResult)
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.compose.ui.window.PopupLayout: void setContent(kotlin.jvm.functions.Function2)
com.example.everytalk.data.DataClass.Part$InlineData: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ThinkingConfig: int hashCode()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.GeminiApiResponse copy$default(com.example.everytalk.data.DataClass.GeminiApiResponse,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,int,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String toString()
androidx.compose.ui.state.ToggleableState: androidx.compose.ui.state.ToggleableState[] values()
androidx.compose.ui.text.font.FontWeightAdjustmentHelperApi31: int fontWeightAdjustment(android.content.Context)
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: void getUseWebSearch$annotations()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion()
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(int,java.lang.String,java.lang.String,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: int hashCode()
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType[] values()
com.example.everytalk.data.DataClass.Candidate: java.lang.String getFinishReason()
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest: void getApiAddress$annotations()
com.example.everytalk.data.DataClass.GithubRelease: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GithubRelease,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.AndroidComposeView: boolean getScrollCaptureInProgress$ui_release()
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction valueOf(java.lang.String)
androidx.compose.ui.input.pointer.PointerEventPass: androidx.compose.ui.input.pointer.PointerEventPass valueOf(java.lang.String)
androidx.core.content.FileProvider: FileProvider()
com.example.everytalk.data.DataClass.ContentPart$Html: int hashCode()
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] values()
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.focus.FocusOwner getFocusOwner()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig)
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String,kotlin.jvm.internal.DefaultConstructorMarker)
kotlin.reflect.KVariance: kotlin.reflect.KVariance valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: Candidate(int,com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
androidx.compose.ui.window.PopupLayout: void setPositionProvider(androidx.compose.ui.window.PopupPositionProvider)
androidx.compose.ui.platform.AndroidComposeView: int getImportantForAutofill()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$InlineData)
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax[] values()
com.example.everytalk.data.network.AppStreamEvent$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getMessages()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.FontFamily$Resolver getFontFamilyResolver()
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType valueOf(java.lang.String)
androidx.compose.ui.graphics.Path$Direction: androidx.compose.ui.graphics.Path$Direction[] values()
kotlinx.serialization.json.JsonArray$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.platform.AbstractComposeView: boolean getHasComposition()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String getText()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.platform.AndroidComposeView: void getShowLayoutBounds$annotations()
okhttp3.internal.publicsuffix.PublicSuffixDatabase: PublicSuffixDatabase()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy$default(com.example.everytalk.data.DataClass.Content,java.util.List,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String component1()
com.example.everytalk.data.network.AppStreamEvent$Finish$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Content: java.lang.String toString()
com.example.everytalk.data.DataClass.ChatRequest: void getCustomModelParameters$annotations()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List getApiConfigs()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getId$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getBase64Data$annotations()
com.example.everytalk.data.DataClass.Message: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Message,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration[] values()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getBody()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.window.PopupLayout: void getParams$ui_release$annotations()
com.example.everytalk.data.network.AppStreamEvent$WebSearchResults$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$Text: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isVisible(int)
org.slf4j.helpers.Reporter$TargetChoice: org.slf4j.helpers.Reporter$TargetChoice[] values()
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component2()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component1()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String toString()
kotlin.reflect.KVariance: kotlin.reflect.KVariance[] values()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax[] values()
com.example.everytalk.data.DataClass.MessageKt: java.lang.String toRole(com.example.everytalk.data.DataClass.Sender)
com.example.everytalk.data.DataClass.ChatRequest: void getGenerationConfig$annotations()
androidx.core.view.WindowInsetsCompat$Impl: boolean isVisible(int)
org.slf4j.nop.NOPServiceProvider: NOPServiceProvider()
androidx.graphics.path.PathIteratorPreApi34Impl: long createInternalPathIterator(android.graphics.Path,int,float)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Content$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.unit.ConstraintsKt: java.lang.Void throwInvalidConstraintsSizeException(int)
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: int hashCode()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: com.example.everytalk.data.DataClass.Part$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: void setDensity(androidx.compose.ui.unit.Density)
androidx.compose.ui.node.LayoutNode$UsageByParent: androidx.compose.ui.node.LayoutNode$UsageByParent[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getTitle()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.compose.ui.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.example.everytalk.data.DataClass.Part: void write$Self(com.example.everytalk.data.DataClass.Part,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillTree getAutofillTree()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
com.example.everytalk.data.network.AppStreamEvent$Content$Companion: kotlinx.serialization.KSerializer serializer()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.Autofill getAutofill()
androidx.compose.runtime.collection.MutableVectorKt: void throwOutOfRangeException(int,int)
com.example.everytalk.data.DataClass.Part$FileUri: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
androidx.compose.runtime.ComposerKt: void composeImmediateRuntimeError(java.lang.String)
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.AndroidDragAndDropManager getDragAndDropManager()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: com.example.everytalk.data.DataClass.PromptFeedback deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.SafetyRating: int hashCode()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component3()
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: int hashCode()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.AbstractApiMessage toApiMessage()
com.example.everytalk.data.DataClass.ContentPart$Html: com.example.everytalk.data.DataClass.ContentPart$Html copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List component3()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorRawSize(long)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getImageSize()
androidx.compose.ui.util.ListUtilsKt: void throwUnsupportedOperationException(java.lang.String)
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize[] values()
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean getIncludeThoughts()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String toString()
com.example.everytalk.data.DataClass.ModalityType: kotlin.enums.EnumEntries getEntries()
androidx.compose.ui.platform.AndroidComposeView: boolean getHasPendingMeasureOrLayout()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component2()
androidx.compose.ui.platform.OutlineVerificationHelper: void setPath(android.graphics.Outline,androidx.compose.ui.graphics.Path)
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(int,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy$default(com.example.everytalk.data.DataClass.GithubRelease,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: long getLastMatrixRecalculationAnimationTime$ui_release()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.lang.String getRole()
androidx.compose.animation.core.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection[] values()
androidx.compose.animation.core.MutatePriority: androidx.compose.animation.core.MutatePriority valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: com.example.everytalk.data.DataClass.GeminiApiResponse deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: java.lang.String component2()
com.example.everytalk.data.DataClass.PromptFeedback: com.example.everytalk.data.DataClass.PromptFeedback copy$default(com.example.everytalk.data.DataClass.PromptFeedback,java.util.List,int,java.lang.Object)
coil3.size.Precision: coil3.size.Precision[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: ApiContentPart$Text$$serializer()
com.example.everytalk.data.DataClass.Content: com.example.everytalk.data.DataClass.Content copy(java.util.List,java.lang.String)
com.example.everytalk.statecontroller.LRUCache: java.util.Set getKeys()
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Part$Text$$serializer: Part$Text$$serializer()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String toString()
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String toString()
com.example.everytalk.data.DataClass.PromptFeedback$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String component1()
io.ktor.util.Platform: io.ktor.util.Platform valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.compose.foundation.layout.IntrinsicSize: androidx.compose.foundation.layout.IntrinsicSize valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.input.pointer.PointerInputEventHandler: java.lang.Object invoke(androidx.compose.ui.input.pointer.PointerInputScope,kotlin.coroutines.Continuation)
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getSafetySettings()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.example.everytalk.data.DataClass.ApiConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy$default(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,java.lang.String,java.lang.String,int,java.lang.Object)
io.ktor.util.date.Month: io.ktor.util.date.Month[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.graphics.GraphicsContext getGraphicsContext()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component3()
androidx.core.view.WindowInsetsCompat$BuilderImpl34: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ApiConfig: boolean isValid()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: com.example.everytalk.data.DataClass.ApiConfig deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: java.lang.String getCurrentWebSearchStage()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Object getToolChoice()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
com.example.everytalk.data.DataClass.Message: long getTimestamp()
okhttp3.TlsVersion: okhttp3.TlsVersion[] values()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
com.example.everytalk.data.DataClass.ApiContentPart$Text: boolean equals(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode[] values()
androidx.compose.ui.window.PopupLayout: void setLayoutDirection(int)
com.example.everytalk.data.DataClass.Message: boolean isPlaceholderName()
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.IntObjectMap getLayoutNodes()
androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight: androidx.compose.ui.node.NodeMeasuringIntrinsics$IntrinsicWidthHeight valueOf(java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.Part$Text: java.lang.String component1()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig()
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption[] values()
io.ktor.util.date.Month: io.ktor.util.date.Month valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: Message(int,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.statecontroller.LRUCache: java.util.Set getEntries()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String toString()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNode getRoot()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getContent$annotations()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AutofillManager getAutofillManager()
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.hapticfeedback.HapticFeedback getHapticFeedBack()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiConfig)
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.SafetyRating: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SafetyRating,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand valueOf(java.lang.String)
androidx.compose.animation.EnterExitState: androidx.compose.animation.EnterExitState[] values()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.platform.ViewLayer: void setInvalidated(boolean)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.window.PopupLayout: android.view.View getViewRoot()
com.example.everytalk.data.DataClass.SafetySetting: com.example.everytalk.data.DataClass.SafetySetting copy$default(com.example.everytalk.data.DataClass.SafetySetting,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Content$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Content)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.ViewConfiguration getViewConfiguration()
okhttp3.Protocol: okhttp3.Protocol valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getApiKey()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.compose.foundation.text.HandleState: androidx.compose.foundation.text.HandleState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: boolean isError()
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getContentId()
com.example.everytalk.data.DataClass.ThinkingConfig: void getThinkingBudget$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
kotlinx.serialization.json.JsonPrimitive$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue valueOf(java.lang.String)
androidx.compose.ui.contentcapture.ContentCaptureEventType: androidx.compose.ui.contentcapture.ContentCaptureEventType[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component1()
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
kotlinx.serialization.json.JsonNull: kotlinx.serialization.KSerializer serializer()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.ApiContentPart$Text: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ApiContentPart$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.spatial.RectManager getRectManager()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.example.everytalk.data.DataClass.GenerationConfig: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: ApiConfig$$serializer()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Candidate$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeViewVerificationHelperMethodsN: void setPointerIcon(android.view.View,androidx.compose.ui.input.pointer.PointerIcon)
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: PartsApiMessage$$serializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getKey()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getName$annotations()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(java.lang.String,java.lang.String)
androidx.compose.ui.platform.AndroidComposeViewForceDarkModeQ: void disallowForceDark(android.view.View)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component1()
androidx.compose.ui.window.PopupLayout: void setTestTag(java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy(java.util.List,java.util.Set)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy$default(com.example.everytalk.data.DataClass.SafetyRating,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component12()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: ChatRequest$$serializer()
com.example.everytalk.data.DataClass.ContentPart$Html: ContentPart$Html(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float component2()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.runtime.PreconditionsKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight valueOf(java.lang.String)
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetyRating)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
com.example.everytalk.data.DataClass.Content: java.util.List getParts()
com.example.everytalk.data.DataClass.Message$Companion: Message$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Candidate: Candidate(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender component3()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.platform.AbstractComposeView: void setShowLayoutBounds(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getLanguage()
com.example.everytalk.data.DataClass.GenerationConfig: int hashCode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: SafetyRating$$serializer()
androidx.compose.ui.text.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.Content$Companion: Content$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.compose.ui.window.PopupLayout: void setParentLayoutDirection(androidx.compose.ui.unit.LayoutDirection)
io.ktor.util.Platform: io.ktor.util.Platform[] values()
com.example.everytalk.data.DataClass.PartsApiMessage: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.PartsApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.core.view.WindowInsetsCompat$Impl30: boolean isVisible(int)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getMimeType()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Boolean component1()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.emoji2.text.EmojiExclusions$EmojiExclusions_Api34: java.util.Set getExclusions()
com.example.everytalk.data.DataClass.Message: long component8()
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map getCustomModelParameters()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.LayoutDirection getLayoutDirection()
com.example.everytalk.data.DataClass.Message$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.SoftwareKeyboardController getSoftwareKeyboardController()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$Text)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component3()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorPeek(long)
androidx.compose.material.ripple.RippleHostView: void setRippleState(boolean)
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy$default(com.example.everytalk.data.DataClass.ApiContentPart$Text,java.lang.String,int,java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.modifier.ModifierLocalManager getModifierLocalManager()
androidx.compose.material3.MinimumInteractiveModifier: MinimumInteractiveModifier()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback getPromptFeedback()
androidx.compose.animation.core.RepeatMode: androidx.compose.animation.core.RepeatMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component3()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.compose.ui.graphics.layer.ViewLayer: android.view.View getOwnerView()
com.example.everytalk.data.DataClass.ChatRequest: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ChatRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.input.TextInputService getTextInputService()
coil3.size.Scale: coil3.size.Scale[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.autofill.AndroidAutofillManager get_autofillManager$ui_release()
androidx.compose.ui.platform.ComposeView: java.lang.CharSequence getAccessibilityClassName()
com.example.everytalk.data.DataClass.PartsApiMessage: void getParts$annotations()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners get_viewTreeOwners()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse()
com.example.everytalk.data.DataClass.ApiContentPart: void write$Self(com.example.everytalk.data.DataClass.ApiContentPart,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$Text: ApiContentPart$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: com.example.everytalk.data.DataClass.ChatRequest deserialize(kotlinx.serialization.encoding.Decoder)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(java.util.List,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.DataClass.ModalityType$Companion: com.example.everytalk.data.DataClass.ModalityType fromDisplayName(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: java.lang.String toString()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$InlineData)
kotlin.LazyThreadSafetyMode: kotlin.LazyThreadSafetyMode[] values()
kotlinx.serialization.json.ClassDiscriminatorMode: kotlinx.serialization.json.ClassDiscriminatorMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
coil3.size.Scale: coil3.size.Scale valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: ApiContentPart$InlineData$$serializer()
com.example.everytalk.data.DataClass.Message: java.util.List component13()
com.example.everytalk.data.DataClass.Content: java.lang.String component2()
com.example.everytalk.data.DataClass.ThinkingConfig$Companion: ThinkingConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.material3.internal.InputPhase: androidx.compose.material3.internal.InputPhase[] values()
com.example.everytalk.data.DataClass.ThinkingConfig: com.example.everytalk.data.DataClass.ThinkingConfig copy(java.lang.Boolean,java.lang.Integer)
com.example.everytalk.data.DataClass.GenerationConfig$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Message: java.lang.String component4()
androidx.compose.ui.layout.IntrinsicMinMax: androidx.compose.ui.layout.IntrinsicMinMax valueOf(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void setContentCaptureManager$ui_release(androidx.compose.ui.contentcapture.AndroidContentCaptureManager)
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.platform.AbstractComposeView getSubCompositionView()
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult[] values()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: PromptFeedback$$serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String component1()
com.example.everytalk.data.DataClass.SafetySetting: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SafetySetting,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ContentPart$Audio: com.example.everytalk.data.DataClass.ContentPart$Audio copy(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.runtime.PreconditionsKt: void throwIllegalArgumentException(java.lang.String)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
com.example.everytalk.statecontroller.LRUCache: java.util.Set keySet()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$FileUri deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: Message(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component1()
com.example.everytalk.data.DataClass.Message: int hashCode()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.compose.foundation.layout.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List getCandidates()
com.example.everytalk.data.DataClass.WebSearchResult: int component1()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.graphics.path.ConicConverter: int internalConicToQuadratics(float[],int,float[],float,float)
io.ktor.serialization.kotlinx.json.KotlinxSerializationJsonExtensionProvider: KotlinxSerializationJsonExtensionProvider()
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.runtime.Recomposer$State: androidx.compose.runtime.Recomposer$State[] values()
com.example.everytalk.data.DataClass.Part$Text$$serializer: com.example.everytalk.data.DataClass.Part$Text deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Content: Content(int,java.util.List,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation[] values()
com.example.everytalk.data.DataClass.ContentPart$Code: com.example.everytalk.data.DataClass.ContentPart$Code copy$default(com.example.everytalk.data.DataClass.ContentPart$Code,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
androidx.compose.ui.platform.AbstractComposeView: void setParentContext(androidx.compose.runtime.CompositionContext)
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
com.example.everytalk.data.DataClass.Message: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Content$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Message: boolean getContentStarted()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(int,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String getContentId()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component4()
com.example.everytalk.data.DataClass.Part$Companion: Part$Companion()
com.example.everytalk.data.DataClass.GenerationConfig: GenerationConfig(java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.SafetySetting: boolean equals(java.lang.Object)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.Part: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
com.example.everytalk.data.DataClass.Message: java.lang.String getId()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Message: java.lang.String getReasoning()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ApiConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.models.SelectedMediaItem$ImageFromUri$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: PromptFeedback(int,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.runtime.collection.MutableVectorKt: void throwReversedIndicesException(int,int)
com.example.everytalk.data.DataClass.PartsApiMessage: int hashCode()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.InputModeManager getInputModeManager()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component7()
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy$default(com.example.everytalk.data.DataClass.ApiContentPart$InlineData,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: int hashCode()
kotlin.DeprecationLevel: kotlin.DeprecationLevel valueOf(java.lang.String)
androidx.compose.foundation.text.Handle: androidx.compose.foundation.text.Handle[] values()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.SafetySetting$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$FileUri: java.lang.String getFileUri()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component9()
androidx.compose.ui.platform.ViewLayer: androidx.compose.ui.platform.AndroidComposeView getOwnerView()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart(int,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.compose.ui.graphics.layer.ViewLayer: void setInvalidated(boolean)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
androidx.compose.ui.unit.ConstraintsKt: void throwInvalidConstraintException(int,int)
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Sender: Sender(java.lang.String,int)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GenerationConfig component2()
androidx.compose.ui.platform.ViewLayer: float[] getUnderlyingMatrix-sQKQjiQ()
com.example.everytalk.data.DataClass.Message$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getMimeType()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List getTools()
com.example.everytalk.data.DataClass.PartsApiMessage: java.util.List getParts()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.layout.LayoutCoordinates getParentLayoutCoordinates()
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$Text: java.lang.String toString()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: SimpleTextApiMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback$Companion: PromptFeedback$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getMimeType()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
org.slf4j.event.Level: org.slf4j.event.Level[] values()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
com.example.everytalk.statecontroller.MainActivity: MainActivity()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$FileUri)
com.example.everytalk.data.DataClass.SafetyRating$Companion: SafetyRating$Companion()
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
com.example.everytalk.data.DataClass.PartsApiMessage: void getName$annotations()
com.example.everytalk.data.DataClass.Content$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.text.font.Font$ResourceLoader getFontLoader()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String component3()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer component11()
androidx.compose.ui.unit.LayoutDirection: androidx.compose.ui.unit.LayoutDirection[] values()
com.example.everytalk.data.DataClass.ThinkingConfig: ThinkingConfig(java.lang.Boolean,java.lang.Integer,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component2()
androidx.compose.ui.platform.AndroidComposeView: void set_viewTreeOwners(androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component1()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.AbstractApiMessage: java.lang.String getName()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String toString()
com.example.everytalk.data.DataClass.Content: Content(java.util.List,java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: void setConfigurationChangeObserver(kotlin.jvm.functions.Function1)
com.example.everytalk.data.DataClass.SafetySetting: java.lang.String getThreshold()
com.example.everytalk.data.DataClass.PartsApiMessage: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ChatRequest: void getQwenEnableSearch$annotations()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.activity.EdgeToEdgeApi21: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List component1()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component5()
com.example.everytalk.data.DataClass.Part$FileUri: Part$FileUri(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy(java.lang.String,java.lang.String,java.util.List,java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: boolean equals(java.lang.Object)
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Candidate,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ApiContentPart$Text: com.example.everytalk.data.DataClass.ApiContentPart$Text copy(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.example.everytalk.data.DataClass.Content$$serializer: com.example.everytalk.data.DataClass.Content deserialize(kotlinx.serialization.encoding.Decoder)
org.slf4j.event.Level: org.slf4j.event.Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType[] $values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component1()
androidx.compose.ui.platform.AndroidComposeView: void setAccessibilityEventBatchIntervalMillis(long)
com.example.everytalk.data.DataClass.AbstractApiMessage: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.IntSize getPopupContentSize-bOM6tXw()
androidx.compose.ui.text.style.ResolvedTextDirection: androidx.compose.ui.text.style.ResolvedTextDirection valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.window.PopupLayout: android.view.WindowManager$LayoutParams getParams$ui_release()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.activity.EdgeToEdgeApi23: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: void getCustomExtraBody$annotations()
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PartsApiMessage)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.List component1()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$Companion: SimpleTextApiMessage$Companion()
com.example.everytalk.data.DataClass.ChatRequest: void getMessages$annotations()
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus[] values()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: com.example.everytalk.data.DataClass.WebSearchResult deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.coroutines.flow.SharingCommand: kotlinx.coroutines.flow.SharingCommand[] values()
com.example.everytalk.data.DataClass.SafetySetting: int hashCode()
com.example.everytalk.data.DataClass.GenerationConfig: void getMaxOutputTokens$annotations()
androidx.lifecycle.ReportFragment: ReportFragment()
coil3.decode.DataSource: coil3.decode.DataSource[] values()
androidx.compose.ui.platform.AndroidComposeViewTranslationCallbackS: void clearViewTranslationCallback(android.view.View)
com.example.everytalk.data.DataClass.SafetySetting$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SafetySetting)
com.example.everytalk.data.DataClass.Content: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig component8()
androidx.compose.ui.platform.AbstractComposeView: void setParentCompositionContext(androidx.compose.runtime.CompositionContext)
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.Message$$serializer: com.example.everytalk.data.DataClass.Message deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(int,java.util.List,java.util.Set,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getBase64Data()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: ApiContentPart$InlineData(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.platform.AndroidComposeView: kotlin.coroutines.CoroutineContext getCoroutineContext()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidComposeView$ViewTreeOwners getViewTreeOwners()
androidx.compose.ui.platform.AndroidComposeView: void setCoroutineContext(kotlin.coroutines.CoroutineContext)
androidx.compose.runtime.SlotTableKt: void throwConcurrentModificationException()
com.example.everytalk.data.DataClass.Part$InlineData: com.example.everytalk.data.DataClass.Part$InlineData copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.ChatRequest: void getForceGoogleReasoningPrompt$annotations()
androidx.navigation.NavControllerViewModel: NavControllerViewModel()
com.example.everytalk.data.DataClass.ThinkingConfig: void getIncludeThoughts$annotations()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
com.example.everytalk.data.DataClass.Part: Part(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.GithubRelease$$serializer: GithubRelease$$serializer()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl34)
com.example.everytalk.data.DataClass.Part$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Part$Text)
com.example.everytalk.data.DataClass.Message: boolean component6()
androidx.compose.ui.platform.AndroidComposeView: kotlin.jvm.functions.Function1 getConfigurationChangeObserver()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean getDefaultUseWebSearch()
com.example.everytalk.data.DataClass.ThinkingConfig: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ChatRequest: java.util.Map component13()
com.example.everytalk.data.DataClass.ChatRequest: void getModel$annotations()
com.example.everytalk.data.DataClass.Candidate$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
kotlinx.serialization.json.DecodeSequenceMode: kotlinx.serialization.json.DecodeSequenceMode valueOf(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void getDisposeViewCompositionStrategy$annotations()
com.example.everytalk.models.SelectedMediaItem$Audio$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getId()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.example.everytalk.data.DataClass.Message: boolean component5()
com.example.everytalk.data.DataClass.ChatRequest: java.util.List component1()
com.example.everytalk.data.DataClass.GeminiApiResponse$Companion: GeminiApiResponse$Companion()
com.example.everytalk.data.DataClass.Part$Text: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$Text,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GithubRelease: com.example.everytalk.data.DataClass.GithubRelease copy(java.lang.String,java.lang.String,java.lang.String)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.example.everytalk.data.DataClass.Sender$Companion: Sender$Companion()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getProvider()
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.Part$Text$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.compose.material3.SheetValue: androidx.compose.material3.SheetValue[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component4()
androidx.compose.ui.platform.AbstractComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
androidx.compose.ui.window.PopupLayout: boolean getShouldCreateCompositionOnAttachedToWindow()
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode[] values()
com.example.everytalk.statecontroller.LRUCache: int size()
com.example.everytalk.data.DataClass.PromptFeedback: java.util.List getSafetyRatings()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion()
com.example.everytalk.data.DataClass.Candidate: java.util.List getSafetyRatings()
com.example.everytalk.data.DataClass.Message: java.lang.String toString()
com.example.everytalk.data.DataClass.Part$FileUri: com.example.everytalk.data.DataClass.Part$FileUri copy$default(com.example.everytalk.data.DataClass.Part$FileUri,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: com.example.everytalk.data.DataClass.Part$InlineData deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part: Part(int,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getHtmlUrl()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: com.example.everytalk.data.DataClass.ApiContentPart$InlineData copy(java.lang.String,java.lang.String)
androidx.compose.ui.graphics.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.LayoutNodeDrawScope getSharedDrawScope()
com.example.everytalk.data.network.AppStreamEvent$StreamEnd$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.ui.platform.ComposeView: void setContent(kotlin.jvm.functions.Function2)
com.example.everytalk.data.DataClass.Candidate: int getIndex()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.ComposeView: void getShouldCreateCompositionOnAttachedToWindow$annotations()
androidx.activity.EdgeToEdgeApi26: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ApiConfig copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
androidx.navigation.compose.DialogNavigator: DialogNavigator()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: Part$InlineData$$serializer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.compose.ui.focus.CustomDestinationResult: androidx.compose.ui.focus.CustomDestinationResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(int,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GenerationConfig)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboard getClipboard()
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Message: java.util.List getImageUrls()
com.example.everytalk.data.DataClass.Candidate$Companion: Candidate$Companion()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component11()
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorSize(long)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: ThinkingConfig$$serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AndroidClipboardManager getClipboardManager()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.WebSearchResult$Companion: com.example.everytalk.data.DataClass.WebSearchResult fromMap(java.util.Map)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl34: WindowInsetsCompat$BuilderImpl34(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.Part$InlineData: Part$InlineData(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Message: boolean component9()
com.example.everytalk.data.DataClass.ApiConfig: boolean component7()
com.example.everytalk.data.DataClass.ContentPart$Code: ContentPart$Code(java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getHolderToLayoutNode()
androidx.compose.foundation.gestures.Orientation: androidx.compose.foundation.gestures.Orientation valueOf(java.lang.String)
coil3.request.CachePolicy: coil3.request.CachePolicy valueOf(java.lang.String)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getCategory()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String component1()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.AndroidComposeView: androidx.collection.MutableIntObjectMap getLayoutNodes()
com.example.everytalk.data.DataClass.ContentPart: ContentPart(java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: com.example.everytalk.statecontroller.AppViewModel$ExportedSettings copy$default(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,java.util.List,java.util.Set,int,java.lang.Object)
androidx.compose.ui.platform.AbstractComposeView: void setPreviousAttachedWindowToken(android.os.IBinder)
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
coil3.size.Precision: coil3.size.Precision valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component1()
com.example.everytalk.data.DataClass.ChatRequest: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.AbstractApiMessage$Companion: AbstractApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.Boolean component6()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
com.example.everytalk.data.DataClass.PartsApiMessage: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig component4()
com.example.everytalk.data.DataClass.ContentPart$Audio: int hashCode()
com.example.everytalk.data.DataClass.WebSearchResult$$serializer: WebSearchResult$$serializer()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getTopP()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.lang.String toString()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ModalityType: ModalityType(java.lang.String,int,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Float getTemperature()
com.example.everytalk.data.DataClass.Candidate: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiResponse: java.lang.String toString()
androidx.compose.runtime.InvalidationResult: androidx.compose.runtime.InvalidationResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Candidate: java.util.List component4()
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(int,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,kotlinx.serialization.internal.SerializationConstructorMarker)
coil3.request.CachePolicy: coil3.request.CachePolicy[] values()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getModel()
com.example.everytalk.data.DataClass.Part$Text: Part$Text(int,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
com.example.everytalk.data.DataClass.ChatRequest: void getToolChoice$annotations()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List component1()
androidx.activity.EdgeToEdgeApi29: void setUp(androidx.activity.SystemBarStyle,androidx.activity.SystemBarStyle,android.view.Window,android.view.View,boolean,boolean)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.ChatRequest$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.compose.material3.tokens.TypographyKeyTokens: androidx.compose.material3.tokens.TypographyKeyTokens[] values()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.node.OwnerSnapshotObserver getSnapshotObserver()
com.example.everytalk.data.DataClass.Candidate: int hashCode()
com.example.everytalk.data.DataClass.Part$Text: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiRequest: int hashCode()
com.example.everytalk.data.DataClass.PartsApiMessage: java.lang.String getName()
androidx.compose.animation.core.AnimationEndReason: androidx.compose.animation.core.AnimationEndReason[] values()
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
androidx.compose.material.ripple.UnprojectedRipple$MRadiusHelper: void setRadius(android.graphics.drawable.RippleDrawable,int)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String component1()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String getUri()
com.example.everytalk.statecontroller.LRUCache: java.util.Collection getValues()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: Part$FileUri$$serializer()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.WindowInfo getWindowInfo()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.ThinkingConfig getThinkingConfig()
coil3.decode.DataSource: coil3.decode.DataSource valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig: void getTopP$annotations()
com.example.everytalk.data.DataClass.Message: java.util.List getAttachments()
com.example.everytalk.data.DataClass.ApiConfig: float component9()
com.example.everytalk.data.DataClass.ContentPart$Html: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$Text: java.lang.String getText()
com.example.everytalk.data.DataClass.Content: java.util.List component1()
androidx.compose.ui.window.PopupLayout: androidx.compose.ui.unit.LayoutDirection getParentLayoutDirection()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
kotlin.io.encoding.Base64$PaddingOption: kotlin.io.encoding.Base64$PaddingOption valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
com.example.everytalk.data.DataClass.Part$InlineData$Companion: Part$InlineData$Companion()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$Companion: ApiContentPart$InlineData$Companion()
androidx.core.view.WindowInsetsCompat$Impl20: boolean systemBarVisibilityEquals(int,int)
kotlinx.serialization.json.JsonObject$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.DrawChildContainer: int getChildCount()
com.example.everytalk.data.DataClass.GeminiApiResponse: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.GithubRelease: void getTagName$annotations()
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.ChatRequest copy$default(com.example.everytalk.data.DataClass.ChatRequest,java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map,int,java.lang.Object)
androidx.compose.ui.platform.actionmodecallback.MenuItemOption: androidx.compose.ui.platform.actionmodecallback.MenuItemOption[] values()
com.example.everytalk.data.DataClass.WebSearchResult: WebSearchResult(int,java.lang.String,java.lang.String,java.lang.String)
androidx.compose.ui.graphics.layer.view.DrawChildContainer: int getChildCount()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.ChatRequest: void getApiKey$annotations()
androidx.compose.ui.platform.AndroidViewsHandler: java.util.HashMap getLayoutNodeToHolder()
com.example.everytalk.data.DataClass.GenerationConfig: void getThinkingConfig$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$Text: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart: ApiContentPart()
com.example.everytalk.data.DataClass.ApiConfig$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PromptFeedback: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Part$FileUri: int hashCode()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(int,java.lang.String,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String getTagName()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isTypeVisible(int)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
kotlinx.serialization.json.internal.WriteMode: kotlinx.serialization.json.internal.WriteMode valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PromptFeedback: boolean equals(java.lang.Object)
androidx.compose.ui.platform.ComposeView: boolean getShouldCreateCompositionOnAttachedToWindow()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Candidate$$serializer: com.example.everytalk.data.DataClass.Candidate deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String component4()
com.example.everytalk.data.DataClass.SafetyRating$Companion: kotlinx.serialization.KSerializer serializer()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.compose.ui.platform.AndroidComposeView: long getMeasureIteration()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: com.example.everytalk.data.DataClass.SafetyRating deserialize(kotlinx.serialization.encoding.Decoder)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: void getHtmlUrl$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.material3.tokens.ShapeKeyTokens: androidx.compose.material3.tokens.ShapeKeyTokens[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setInsets(int,androidx.core.graphics.Insets)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GeminiApiRequest)
androidx.compose.ui.layout.IntrinsicWidthHeight: androidx.compose.ui.layout.IntrinsicWidthHeight[] values()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getName()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getName()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.GeminiApiResponse: java.util.List component1()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: ApiContentPart$FileUri(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: void write$Self$app_benchmark(com.example.everytalk.statecontroller.AppViewModel$ExportedSettings,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Candidate)
androidx.compose.runtime.collection.MutableVectorKt: void throwListIndexOutOfBoundsException(int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.compose.material3.ColorResourceHelper: long getColor-WaAFU9c(android.content.Context,int)
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus valueOf(java.lang.String)
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String getCode()
com.example.everytalk.data.DataClass.GenerationConfig: com.example.everytalk.data.DataClass.GenerationConfig copy$default(com.example.everytalk.data.DataClass.GenerationConfig,java.lang.Float,java.lang.Float,java.lang.Integer,com.example.everytalk.data.DataClass.ThinkingConfig,int,java.lang.Object)
com.example.everytalk.data.network.AppStreamEvent$Reasoning$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy valueOf(java.lang.String)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.example.everytalk.data.DataClass.GeminiApiRequest: GeminiApiRequest(java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List)
com.example.everytalk.data.DataClass.Content$$serializer: Content$$serializer()
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level[] values()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float,int,kotlin.jvm.internal.DefaultConstructorMarker)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.example.everytalk.data.DataClass.WebSearchResult: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.WebSearchResult,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ContentPart$Code: java.lang.String component2()
com.example.everytalk.data.DataClass.ApiContentPart$Companion: ApiContentPart$Companion()
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
com.example.everytalk.data.DataClass.Sender$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Integer getMaxTokens()
com.example.everytalk.data.DataClass.Sender: kotlin.enums.EnumEntries getEntries()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.ApiContentPart$FileUri)
com.example.everytalk.data.DataClass.Part$InlineData$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiResponse: GeminiApiResponse(int,java.util.List,com.example.everytalk.data.DataClass.PromptFeedback,kotlinx.serialization.internal.SerializationConstructorMarker)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String component4()
com.example.everytalk.data.DataClass.Message: java.lang.String component11()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component10()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.ui.platform.AndroidCompositionLocals_androidKt: androidx.compose.runtime.ProvidableCompositionLocal getLocalLifecycleOwner()
com.example.everytalk.data.DataClass.ApiConfig: float getTemperature()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.SimpleTextApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.graphics.path.PathIteratorPreApi34Impl: void destroyInternalPathIterator(long)
androidx.compose.foundation.text.TextContextMenuItems: androidx.compose.foundation.text.TextContextMenuItems[] values()
com.example.everytalk.data.DataClass.Content: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Content,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GeminiApiRequest: com.example.everytalk.data.DataClass.GeminiApiRequest copy$default(com.example.everytalk.data.DataClass.GeminiApiRequest,java.util.List,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,int,java.lang.Object)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.draganddrop.DragAndDropManager getDragAndDropManager()
com.example.everytalk.data.DataClass.Part$FileUri$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Part$InlineData$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Message: java.util.List component10()
androidx.compose.ui.platform.CalculateMatrixToWindowApi29: void calculateMatrixToWindow-EL8BTi8(android.view.View,float[])
com.example.everytalk.data.DataClass.ApiContentPart$FileUri$Companion: ApiContentPart$FileUri$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: void setInsets(int,androidx.core.graphics.Insets)
androidx.compose.ui.window.PopupLayout: java.lang.String getTestTag()
com.example.everytalk.data.DataClass.ApiConfig: com.example.everytalk.data.DataClass.ModalityType component8()
com.example.everytalk.data.DataClass.GeminiApiRequest$Companion: GeminiApiRequest$Companion()
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.input.pointer.PointerIconService getPointerIconService()
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
io.ktor.util.date.WeekDay: io.ktor.util.date.WeekDay[] values()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Boolean component12()
androidx.compose.ui.platform.AndroidComposeView: void getFontLoader$annotations()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: com.example.everytalk.data.DataClass.ApiContentPart$FileUri copy(java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.Part$Text: boolean equals(java.lang.Object)
androidx.compose.ui.autofill.AutofillType: androidx.compose.ui.autofill.AutofillType[] values()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: AppViewModel$ExportedSettings(java.util.List,java.util.Set)
com.example.everytalk.data.DataClass.ContentPart$Code: boolean equals(java.lang.Object)
com.example.everytalk.models.SelectedMediaItem$GenericFile$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.GeminiApiRequest: java.util.List getContents()
com.example.everytalk.data.DataClass.Candidate$$serializer: Candidate$$serializer()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: com.example.everytalk.data.DataClass.SafetySetting deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$InlineData: java.lang.String getData()
com.example.everytalk.data.DataClass.Message: java.lang.String getName()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getRole()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getProvider()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getData()
com.example.everytalk.data.DataClass.GeminiApiResponse: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String getAddress()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.String toString()
com.example.everytalk.data.DataClass.ChatRequest: java.lang.String getModel()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsetsIgnoringVisibility(int)
com.example.everytalk.data.DataClass.ThinkingConfig: java.lang.Integer component2()
com.example.everytalk.data.DataClass.Part$FileUri: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.Part$FileUri,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
androidx.compose.ui.platform.ViewLayer: long getOwnerViewId()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax: androidx.compose.ui.layout.MeasuringIntrinsics$IntrinsicMinMax[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction: androidx.compose.ui.node.TraversableNode$Companion$TraverseDescendantsAction valueOf(java.lang.String)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart: kotlin.Lazy access$get$cachedSerializer$delegate$cp()
androidx.compose.ui.platform.AndroidComposeView: void getTextInputService$annotations()
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus valueOf(java.lang.String)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: android.window.OnBackInvokedCallback createBackCallback(kotlin.jvm.functions.Function0)
androidx.compose.foundation.text.selection.SelectionHandleAnchor: androidx.compose.foundation.text.selection.SelectionHandleAnchor[] values()
com.example.everytalk.data.DataClass.ApiConfig: ApiConfig(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean,com.example.everytalk.data.DataClass.ModalityType,float,java.lang.Float,java.lang.Integer,java.lang.Boolean,java.lang.String,java.lang.Integer,java.lang.Float)
androidx.compose.ui.graphics.AndroidPath_androidKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.node.LayoutNode: java.lang.String exceptionMessageForParentingOrOwnership(androidx.compose.ui.node.LayoutNode)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.compose.ui.platform.AndroidComposeView: android.view.View findViewByAccessibilityIdTraversal(int)
com.example.everytalk.data.DataClass.Part$Text: Part$Text(java.lang.String)
com.example.everytalk.data.DataClass.GeminiApiRequest$$serializer: com.example.everytalk.data.DataClass.GeminiApiRequest deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.ChatRequest: ChatRequest(java.util.List,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Boolean,java.lang.Boolean,com.example.everytalk.data.DataClass.GenerationConfig,java.util.List,java.lang.Object,java.lang.Boolean,java.util.Map,java.util.Map)
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float getGuidanceScale()
com.example.everytalk.data.DataClass.ChatRequest: com.example.everytalk.data.DataClass.GenerationConfig getGenerationConfig()
com.example.everytalk.data.DataClass.Candidate: int component3()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand: androidx.compose.ui.text.input.TextInputServiceAndroid$TextInputCommand[] values()
com.example.everytalk.data.DataClass.SafetySetting$$serializer: SafetySetting$$serializer()
com.example.everytalk.data.DataClass.Content: java.lang.String getRole()
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode[] values()
com.example.everytalk.data.DataClass.Part$Text$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage()
com.example.everytalk.util.IncrementalMarkdownParser$TokenType: com.example.everytalk.util.IncrementalMarkdownParser$TokenType[] values()
com.example.everytalk.data.DataClass.Part$Text$Companion: Part$Text$Companion()
org.slf4j.helpers.Reporter$Level: org.slf4j.helpers.Reporter$Level valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: void getMimeType$annotations()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: java.lang.String getId()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
com.example.everytalk.statecontroller.LRUCache: java.util.Set entrySet()
com.example.everytalk.data.DataClass.GithubRelease: GithubRelease(java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set getCustomProviders()
com.example.everytalk.data.DataClass.PromptFeedback$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.PromptFeedback)
com.example.everytalk.data.network.AppStreamEvent$ToolCall$Companion: kotlinx.serialization.KSerializer serializer()
androidx.activity.ComponentActivity: void setContentView(android.view.View)
com.example.everytalk.data.DataClass.SafetyRating: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Part$Companion: kotlinx.serialization.KSerializer get$cachedSerializer()
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
com.example.everytalk.statecontroller.AppViewModel$ExportedSettings: java.util.Set component2()
androidx.navigation.compose.ComposeNavigator: ComposeNavigator()
com.example.everytalk.data.DataClass.Message$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.Message)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.text.AnnotationType: androidx.compose.ui.text.AnnotationType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String component1()
com.example.everytalk.models.SelectedMediaItem$ImageFromBitmap$Companion: kotlinx.serialization.KSerializer serializer()
coil3.network.okhttp.internal.OkHttpNetworkFetcherServiceLoaderTarget: OkHttpNetworkFetcherServiceLoaderTarget()
com.example.everytalk.data.DataClass.GenerationConfig: void getTemperature$annotations()
androidx.compose.foundation.layout.Direction: androidx.compose.foundation.layout.Direction[] values()
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getHref()
com.example.everytalk.data.DataClass.ChatRequest$Companion: ChatRequest$Companion()
androidx.compose.ui.platform.AndroidComposeViewStartDragAndDropN: boolean startDragAndDrop(android.view.View,androidx.compose.ui.draganddrop.DragAndDropTransferData,androidx.compose.ui.draganddrop.ComposeDragShadowBuilder)
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Sender getSender()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component2()
com.example.everytalk.statecontroller.LRUCache: int getSize()
androidx.compose.material3.tokens.ColorSchemeKeyTokens: androidx.compose.material3.tokens.ColorSchemeKeyTokens[] values()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: com.example.everytalk.data.DataClass.SimpleTextApiMessage copy$default(com.example.everytalk.data.DataClass.SimpleTextApiMessage,java.lang.String,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.GeminiApiResponse: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.navigation.NavBackStackEntry$SavedStateViewModel: NavBackStackEntry$SavedStateViewModel(androidx.lifecycle.SavedStateHandle)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.TextToolbar getTextToolbar()
com.example.everytalk.statecontroller.LRUCache: boolean removeEldestEntry(java.util.Map$Entry)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.PartsApiMessage: PartsApiMessage(java.lang.String,java.lang.String,java.util.List,java.lang.String,int,kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.Sender: com.example.everytalk.data.DataClass.Sender[] values()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: java.lang.String toString()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy$default(com.example.everytalk.data.DataClass.Message,java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List,int,java.lang.Object)
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption[] values()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.foundation.text.KeyCommand: androidx.compose.foundation.text.KeyCommand[] values()
com.example.everytalk.data.DataClass.GithubRelease$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.GithubRelease)
com.example.everytalk.data.DataClass.ApiConfig$Companion: ApiConfig$Companion()
com.example.everytalk.data.DataClass.ChatRequest: void getTools$annotations()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage: void getRole$annotations()
androidx.core.view.WindowInsetsCompat$Impl34: WindowInsetsCompat$Impl34(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.compose.material3.SnackbarDuration: androidx.compose.material3.SnackbarDuration valueOf(java.lang.String)
androidx.graphics.path.PathIteratorPreApi34Impl: int internalPathIteratorNext(long,float[],int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api34Impl: android.window.OnBackAnimationCallback createBackCallback(kotlin.jvm.functions.Function0,androidx.compose.animation.core.Animatable,kotlinx.coroutines.CoroutineScope)
com.example.everytalk.models.MoreOptionsType: com.example.everytalk.models.MoreOptionsType[] values()
androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus: androidx.compose.ui.contentcapture.AndroidContentCaptureManager$TranslateStatus[] values()
com.example.everytalk.ui.screens.MainScreen.AiMessageOption: com.example.everytalk.ui.screens.MainScreen.AiMessageOption valueOf(java.lang.String)
com.example.everytalk.data.DataClass.Sender: kotlinx.serialization.KSerializer _init_$_anonymous_()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,com.example.everytalk.data.DataClass.SimpleTextApiMessage)
androidx.compose.foundation.internal.InlineClassHelperKt: void throwIndexOutOfBoundsException(java.lang.String)
androidx.activity.EdgeToEdgeApi30: void adjustLayoutInDisplayCutoutMode(android.view.Window)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.compose.material3.internal.TextFieldType: androidx.compose.material3.internal.TextFieldType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: PartsApiMessage$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.GeminiApiRequest: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GeminiApiRequest,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String toString()
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl[] values()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.example.everytalk.data.DataClass.SimpleTextApiMessage$$serializer: SimpleTextApiMessage$$serializer()
com.example.everytalk.data.network.AppStreamEvent$Text$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl: void setSystemUiVisibility(int)
com.example.everytalk.data.DataClass.ContentPart: java.lang.String getContentId()
com.example.everytalk.data.DataClass.GeminiApiResponse: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.GeminiApiResponse,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ModalityType$Companion: ModalityType$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalArgumentExceptionForNullCheck(java.lang.String)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage: com.example.everytalk.data.DataClass.PartsApiMessage copy$default(com.example.everytalk.data.DataClass.PartsApiMessage,java.lang.String,java.lang.String,java.util.List,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.WebSearchResult: com.example.everytalk.data.DataClass.WebSearchResult copy$default(com.example.everytalk.data.DataClass.WebSearchResult,int,java.lang.String,java.lang.String,java.lang.String,int,java.lang.Object)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: com.example.everytalk.data.DataClass.PartsApiMessage deserialize(kotlinx.serialization.encoding.Decoder)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.example.everytalk.data.DataClass.SafetyRating: java.lang.String getProbability()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
com.example.everytalk.data.DataClass.GenerationConfig: java.lang.Integer component3()
io.ktor.client.engine.android.AndroidEngineContainer: AndroidEngineContainer()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Candidate copy(com.example.everytalk.data.DataClass.Content,java.lang.String,int,java.util.List)
com.example.everytalk.data.DataClass.SafetyRating$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
com.example.everytalk.data.DataClass.PartsApiMessage$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.node.LookaheadPassDelegate$PlacedState: androidx.compose.ui.node.LookaheadPassDelegate$PlacedState valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
com.example.everytalk.data.DataClass.ThinkingConfig: void write$Self$app_benchmark(com.example.everytalk.data.DataClass.ThinkingConfig,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.ChatRequest$Companion: kotlinx.serialization.KSerializer serializer()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus valueOf(java.lang.String)
androidx.compose.foundation.text.selection.Direction: androidx.compose.foundation.text.selection.Direction[] values()
androidx.compose.ui.focus.FocusStateImpl: androidx.compose.ui.focus.FocusStateImpl valueOf(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: boolean equals(java.lang.Object)
com.example.everytalk.data.DataClass.Candidate: com.example.everytalk.data.DataClass.Content getContent()
androidx.compose.ui.platform.AndroidComposeView: android.view.View getView()
com.example.everytalk.data.DataClass.AbstractApiMessage: AbstractApiMessage(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
com.example.everytalk.data.DataClass.Part$Text$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.Float component15()
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String toString()
com.example.everytalk.data.DataClass.GeminiApiResponse$$serializer: java.lang.Object deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.Part$FileUri$Companion: kotlinx.serialization.KSerializer serializer()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
com.example.everytalk.data.DataClass.Candidate$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl34: androidx.core.graphics.Insets getInsets(int)
com.example.everytalk.data.DataClass.AbstractApiMessage: void write$Self(com.example.everytalk.data.DataClass.AbstractApiMessage,kotlinx.serialization.encoding.CompositeEncoder,kotlinx.serialization.descriptors.SerialDescriptor)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.descriptors.SerialDescriptor getDescriptor()
com.example.everytalk.data.DataClass.Message: com.example.everytalk.data.DataClass.Message copy(java.lang.String,java.lang.String,com.example.everytalk.data.DataClass.Sender,java.lang.String,boolean,boolean,java.lang.String,long,boolean,java.util.List,java.lang.String,java.util.List,java.util.List)
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.platform.AbstractComposeView: void setViewCompositionStrategy(androidx.compose.ui.platform.ViewCompositionStrategy)
okhttp3.TlsVersion: okhttp3.TlsVersion valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String getSnippet()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
com.example.everytalk.data.DataClass.ApiContentPart$InlineData: java.lang.String getMimeType()
androidx.compose.ui.platform.TextToolbarStatus: androidx.compose.ui.platform.TextToolbarStatus[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String getContentId()
com.example.everytalk.data.DataClass.Candidate: kotlinx.serialization.KSerializer[] access$get$childSerializers$cp()
androidx.compose.ui.unit.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] typeParametersSerializers()
androidx.compose.ui.platform.ViewLayerContainer: void dispatchGetDisplayList()
androidx.compose.ui.platform.AndroidComposeView: void setFontFamilyResolver(androidx.compose.ui.text.font.FontFamily$Resolver)
com.example.everytalk.data.DataClass.SafetyRating: SafetyRating(java.lang.String,java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.ktor.client.plugins.cache.ValidateStatus: io.ktor.client.plugins.cache.ValidateStatus[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
com.example.everytalk.data.DataClass.SafetySetting: SafetySetting(int,java.lang.String,java.lang.String,kotlinx.serialization.internal.SerializationConstructorMarker)
com.example.everytalk.data.DataClass.Candidate$Companion: kotlinx.serialization.KSerializer serializer()
com.example.everytalk.data.DataClass.ApiConfig: java.lang.String component13()
com.example.everytalk.data.DataClass.WebSearchResult: int hashCode()
androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy: androidx.compose.ui.input.pointer.util.VelocityTracker1D$Strategy[] values()
com.example.everytalk.data.DataClass.ModalityType: com.example.everytalk.data.DataClass.ModalityType valueOf(java.lang.String)
com.example.everytalk.data.DataClass.PartsApiMessage$$serializer: void serialize(kotlinx.serialization.encoding.Encoder,java.lang.Object)
androidx.compose.foundation.text.selection.CrossStatus: androidx.compose.foundation.text.selection.CrossStatus valueOf(java.lang.String)
androidx.compose.material3.ModalBottomSheetDialogLayout$Api33Impl: void maybeUnregisterBackCallback(android.view.View,java.lang.Object)
androidx.compose.ui.window.SecureFlagPolicy: androidx.compose.ui.window.SecureFlagPolicy valueOf(java.lang.String)
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
com.example.everytalk.data.DataClass.WebSearchResult: java.lang.String toString()
com.example.everytalk.data.DataClass.SafetyRating: com.example.everytalk.data.DataClass.SafetyRating copy(java.lang.String,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalStateException(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.platform.AccessibilityManager getAccessibilityManager()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
com.example.everytalk.data.DataClass.SimpleTextApiMessage: int hashCode()
com.example.everytalk.data.DataClass.ApiContentPart$Text$Companion: ApiContentPart$Text$Companion()
com.example.everytalk.data.DataClass.GenerationConfig$$serializer: kotlinx.serialization.KSerializer[] childSerializers()
okhttp3.Protocol: okhttp3.Protocol[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
com.example.everytalk.data.DataClass.WebSearchResult$Companion: WebSearchResult$Companion()
androidx.compose.material3.ScaffoldLayoutContent: androidx.compose.material3.ScaffoldLayoutContent[] values()
com.example.everytalk.data.DataClass.GenerationConfig$Companion: GenerationConfig$Companion(kotlin.jvm.internal.DefaultConstructorMarker)
androidx.compose.material3.SnackbarResult: androidx.compose.material3.SnackbarResult[] values()
androidx.compose.foundation.internal.InlineClassHelperKt: java.lang.Void throwIllegalStateExceptionForNullCheck(java.lang.String)
androidx.compose.ui.platform.AndroidComposeView: androidx.compose.ui.unit.Density getDensity()
androidx.compose.ui.internal.InlineClassHelperKt: void throwIllegalArgumentException(java.lang.String)
com.example.everytalk.data.DataClass.ContentPart$Audio: java.lang.String component3()
androidx.compose.ui.window.PopupLayout: void setParentLayoutCoordinates(androidx.compose.ui.layout.LayoutCoordinates)
com.example.everytalk.data.DataClass.ContentPart$Html: java.lang.String component2()
kotlin.DeprecationLevel: kotlin.DeprecationLevel[] values()
com.example.everytalk.data.DataClass.ApiContentPart$Text$$serializer: com.example.everytalk.data.DataClass.ApiContentPart$Text deserialize(kotlinx.serialization.encoding.Decoder)
com.example.everytalk.data.DataClass.IMessage: java.lang.String getId()
com.example.everytalk.data.DataClass.GithubRelease: java.lang.String component3()
com.example.everytalk.data.DataClass.SafetySetting$Companion: SafetySetting$Companion()
com.example.everytalk.data.DataClass.ThinkingConfig$$serializer: com.example.everytalk.data.DataClass.ThinkingConfig deserialize(kotlinx.serialization.encoding.Decoder)
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
com.example.everytalk.data.DataClass.GeminiApiResponse: com.example.everytalk.data.DataClass.PromptFeedback component2()
com.example.everytalk.data.DataClass.ApiContentPart$FileUri: void getMimeType$annotations()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
